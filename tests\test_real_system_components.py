#!/usr/bin/env python3
"""
Real System Components Test
Tests actual bybit_bot system components with live data
NO MOCK DATA - Live system testing only
"""

import pytest
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger
from bybit_bot.database.connection import DatabaseManager
from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
from bybit_bot.profit_maximization.hyper_profit_engine import HyperProfitEngine
from bybit_bot.agents.agent_orchestrator import AgentOrchestrator
from bybit_bot.ai.memory_manager import PersistentMemoryManager


class TestRealSystemComponents:
    """Test real system components with live configuration"""
    
    def setup_method(self):
        """Setup test with real configuration"""
        self.config = BotConfig()
        self.logger = TradingBotLogger()
        
    def test_config_loading(self):
        """Test real configuration loading"""
        assert self.config is not None
        assert hasattr(self.config, 'load_config')
        
    def test_logger_initialization(self):
        """Test real logger initialization"""
        assert self.logger is not None
        assert hasattr(self.logger, 'info')
        assert hasattr(self.logger, 'error')
        
    def test_database_manager(self):
        """Test real database manager"""
        db_manager = DatabaseManager()
        assert db_manager is not None
        assert hasattr(db_manager, 'connect')
        
    def test_enhanced_bybit_client(self):
        """Test real enhanced Bybit client"""
        client = EnhancedBybitClient()
        assert client is not None
        assert hasattr(client, 'get_market_data')
        
    def test_hyper_profit_engine(self):
        """Test real hyper profit engine"""
        engine = HyperProfitEngine()
        assert engine is not None
        assert hasattr(engine, 'analyze_market')
        
    def test_agent_orchestrator(self):
        """Test real agent orchestrator"""
        orchestrator = AgentOrchestrator()
        assert orchestrator is not None
        assert hasattr(orchestrator, 'coordinate_agents')
        
    def test_memory_manager(self):
        """Test real persistent memory manager"""
        memory = PersistentMemoryManager()
        assert memory is not None
        assert hasattr(memory, 'store_memory')


class TestSystemIntegration:
    """Test real system integration workflows"""
    
    def test_trading_pipeline(self):
        """Test real trading pipeline integration"""
        # Test actual trading components working together
        config = BotConfig()
        client = EnhancedBybitClient()
        engine = HyperProfitEngine()
        
        # Verify components can interact
        assert config is not None
        assert client is not None
        assert engine is not None
        
    def test_ai_system_integration(self):
        """Test real AI system integration"""
        orchestrator = AgentOrchestrator()
        memory = PersistentMemoryManager()
        
        # Verify AI components can interact
        assert orchestrator is not None
        assert memory is not None


if __name__ == "__main__":
    # Run real system tests
    pytest.main([__file__, "-v"])
