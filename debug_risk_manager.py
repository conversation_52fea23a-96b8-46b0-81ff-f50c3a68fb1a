#!/usr/bin/env python3
"""
Debug script for Risk Manager
Tests and debugs the risk management system
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger, setup_logging
from bybit_bot.risk.risk_manager import RiskManager
from bybit_bot.database.connection import DatabaseManager


async def debug_risk_manager():
    """Debug the risk manager"""
    
    # Setup logging
    setup_logging(log_level="DEBUG", console_output=True)
    logger = TradingBotLogger("DebugRiskManager")
    
    logger.info("Starting Risk Manager Debug Session")
    
    try:
        # Initialize configuration
        config = BotConfig()
        logger.info("Configuration loaded successfully")
        
        # Initialize database
        db_manager = DatabaseManager(config)
        await db_manager.connect()
        logger.info("Database connected")
        
        # Initialize risk manager
        risk_manager = RiskManager(config, db_manager)
        await risk_manager.initialize()
        logger.info("Risk Manager initialized")
        
        # Test portfolio risk assessment
        logger.info("Testing portfolio risk assessment...")
        
        # Sample portfolio data
        portfolio = {
            'BTCUSDT': {'position': 0.1, 'value': 5000, 'pnl': 150},
            'ETHUSDT': {'position': 2.0, 'value': 3000, 'pnl': -50},
            'ADAUSDT': {'position': 1000, 'value': 500, 'pnl': 25}
        }
        
        risk_assessment = await risk_manager.assess_portfolio_risk(portfolio)
        logger.info(f"Portfolio risk assessment: {risk_assessment}")
        
        # Test position sizing
        logger.info("Testing position sizing...")
        
        account_balance = 10000  # $10,000
        symbol = 'BTCUSDT'
        entry_price = 50000
        stop_loss = 48000
        
        position_size = await risk_manager.calculate_position_size(
            account_balance, symbol, entry_price, stop_loss
        )
        logger.info(f"Recommended position size for {symbol}: {position_size}")
        
        # Test trade validation
        logger.info("Testing trade validation...")
        
        trade_request = {
            'symbol': 'BTCUSDT',
            'side': 'buy',
            'quantity': 0.1,
            'price': 50000,
            'stop_loss': 48000,
            'take_profit': 55000
        }
        
        validation_result = await risk_manager.validate_trade(trade_request, portfolio)
        logger.info(f"Trade validation result: {validation_result}")
        
        # Test risk limits
        logger.info("Testing risk limits...")
        
        # Test max position size limit
        large_trade = trade_request.copy()
        large_trade['quantity'] = 10.0  # Very large position
        
        large_trade_validation = await risk_manager.validate_trade(large_trade, portfolio)
        logger.info(f"Large trade validation: {large_trade_validation}")
        
        # Test correlation risk
        logger.info("Testing correlation risk...")
        correlation_risk = await risk_manager.check_correlation_risk(portfolio, 'BTCUSDT')
        logger.info(f"Correlation risk for BTCUSDT: {correlation_risk}")
        
        # Test drawdown monitoring
        logger.info("Testing drawdown monitoring...")
        
        # Simulate trading history
        trading_history = [
            {'timestamp': '2024-01-01', 'pnl': 100, 'balance': 10100},
            {'timestamp': '2024-01-02', 'pnl': -50, 'balance': 10050},
            {'timestamp': '2024-01-03', 'pnl': 200, 'balance': 10250},
            {'timestamp': '2024-01-04', 'pnl': -300, 'balance': 9950},
            {'timestamp': '2024-01-05', 'pnl': 150, 'balance': 10100}
        ]
        
        drawdown_analysis = await risk_manager.analyze_drawdown(trading_history)
        logger.info(f"Drawdown analysis: {drawdown_analysis}")
        
        # Test volatility assessment
        logger.info("Testing volatility assessment...")
        
        # Sample price data
        price_data = [50000, 51000, 49500, 52000, 48000, 53000, 47000, 54000]
        volatility = await risk_manager.calculate_volatility(price_data)
        logger.info(f"Volatility assessment: {volatility}")
        
        # Test emergency stop conditions
        logger.info("Testing emergency stop conditions...")
        
        emergency_portfolio = {
            'BTCUSDT': {'position': 1.0, 'value': 5000, 'pnl': -2000},  # Large loss
            'ETHUSDT': {'position': 10.0, 'value': 3000, 'pnl': -1500}   # Large loss
        }
        
        emergency_check = await risk_manager.check_emergency_conditions(emergency_portfolio)
        logger.info(f"Emergency conditions check: {emergency_check}")
        
        # Test risk metrics calculation
        logger.info("Testing risk metrics calculation...")
        risk_metrics = await risk_manager.calculate_risk_metrics(portfolio)
        logger.info(f"Risk metrics: {risk_metrics}")
        
        # Test risk reporting
        logger.info("Testing risk reporting...")
        risk_report = await risk_manager.generate_risk_report(portfolio)
        logger.info(f"Risk report generated: {risk_report is not None}")
        
        logger.info("Risk Manager debug session completed successfully")
        
    except Exception as e:
        logger.error(f"Debug session failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        if 'db_manager' in locals():
            await db_manager.close()
            logger.info("Database connection closed")


if __name__ == "__main__":
    print("Risk Manager Debug Session")
    print("=" * 50)
    
    # Run debug session
    asyncio.run(debug_risk_manager())
    
    print("=" * 50)
    print("Debug session completed")
