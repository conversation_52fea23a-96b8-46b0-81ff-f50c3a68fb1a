#!/usr/bin/env python3
"""
Debug script for Enhanced Time Manager
Tests and debugs the time management and market timing system
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timezone, timedelta

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger, setup_logging
from bybit_bot.core.enhanced_time_manager import EnhancedTimeManager
from bybit_bot.database.connection import DatabaseManager


async def debug_time_manager():
    """Debug the enhanced time manager"""
    
    # Setup logging
    setup_logging(log_level="DEBUG", console_output=True)
    logger = TradingBotLogger("DebugTimeManager")
    
    logger.info("Starting Enhanced Time Manager Debug Session")
    
    try:
        # Initialize configuration
        config = BotConfig()
        logger.info("Configuration loaded successfully")
        
        # Initialize database
        db_manager = DatabaseManager(config)
        await db_manager.connect()
        logger.info("Database connected")
        
        # Initialize time manager
        time_manager = EnhancedTimeManager(config, db_manager)
        await time_manager.initialize()
        logger.info("Enhanced Time Manager initialized")
        
        # Test current time functions
        logger.info("Testing current time functions...")
        
        current_time = time_manager.get_current_time()
        logger.info(f"Current time: {current_time}")
        
        current_timestamp = time_manager.get_current_timestamp()
        logger.info(f"Current timestamp: {current_timestamp}")
        
        # Test timezone handling
        logger.info("Testing timezone handling...")
        
        utc_time = time_manager.get_utc_time()
        logger.info(f"UTC time: {utc_time}")
        
        # Test different timezone conversions
        timezones = ['US/Eastern', 'Europe/London', 'Asia/Tokyo', 'Asia/Shanghai']
        for tz in timezones:
            try:
                tz_time = time_manager.convert_timezone(current_time, tz)
                logger.info(f"Time in {tz}: {tz_time}")
            except Exception as e:
                logger.warning(f"Timezone conversion failed for {tz}: {e}")
        
        # Test market session detection
        logger.info("Testing market session detection...")
        
        current_session = time_manager.get_current_market_session()
        logger.info(f"Current market session: {current_session}")
        
        next_session = time_manager.get_next_session_change()
        logger.info(f"Next session change: {next_session}")
        
        # Test trading day classification
        logger.info("Testing trading day classification...")
        
        today = datetime.now(timezone.utc)
        trading_day_type = time_manager.get_trading_day_type(today)
        logger.info(f"Today's trading day type: {trading_day_type}")
        
        # Test for next few days
        for i in range(1, 8):
            future_date = today + timedelta(days=i)
            day_type = time_manager.get_trading_day_type(future_date)
            logger.info(f"{future_date.strftime('%Y-%m-%d %A')}: {day_type}")
        
        # Test market timing analysis
        logger.info("Testing market timing analysis...")
        
        timing_analysis = time_manager.analyze_market_timing()
        logger.info(f"Market timing analysis: {timing_analysis}")
        
        # Test optimal trading hours
        logger.info("Testing optimal trading hours...")
        
        optimal_hours = time_manager.get_optimal_trading_hours()
        logger.info(f"Optimal trading hours: {optimal_hours}")
        
        current_hour = datetime.now(timezone.utc).hour
        is_optimal = time_manager.is_optimal_trading_time()
        logger.info(f"Current hour {current_hour} is optimal: {is_optimal}")
        
        # Test volatility and volume predictions
        logger.info("Testing volatility and volume predictions...")
        
        for hour in [0, 6, 12, 18]:  # Test different hours
            volatility = time_manager.get_expected_volatility(hour)
            volume = time_manager.get_expected_volume(hour)
            logger.info(f"Hour {hour:02d}: Volatility={volatility:.2f}, Volume={volume:.2f}")
        
        # Test market calendar
        logger.info("Testing market calendar...")
        
        # Get calendar for next 7 days
        start_date = datetime.now(timezone.utc).date()
        for i in range(7):
            check_date = start_date + timedelta(days=i)
            calendar_info = time_manager.get_market_calendar_info(check_date)
            logger.info(f"{check_date}: {calendar_info}")
        
        # Test holiday detection
        logger.info("Testing holiday detection...")
        
        is_holiday_today = time_manager.is_holiday()
        logger.info(f"Today is holiday: {is_holiday_today}")
        
        next_holiday = time_manager.get_next_holiday()
        logger.info(f"Next holiday: {next_holiday}")
        
        # Test session statistics
        logger.info("Testing session statistics...")
        
        session_stats = time_manager.get_session_statistics()
        logger.info(f"Session statistics: {session_stats}")
        
        # Test time-based recommendations
        logger.info("Testing time-based recommendations...")
        
        recommendations = time_manager.get_trading_recommendations()
        logger.info(f"Trading recommendations: {recommendations}")
        
        # Test performance by time
        logger.info("Testing performance by time analysis...")
        
        current_hour = datetime.now(timezone.utc).hour
        hour_performance = time_manager.get_hour_performance(current_hour)
        logger.info(f"Performance for hour {current_hour}: {hour_performance}")
        
        # Test time until next optimal period
        logger.info("Testing time calculations...")
        
        time_to_optimal = time_manager.get_time_until_optimal()
        logger.info(f"Time until next optimal period: {time_to_optimal}")
        
        # Test market activity prediction
        logger.info("Testing market activity prediction...")
        
        activity_forecast = time_manager.forecast_market_activity(hours_ahead=6)
        logger.info(f"6-hour activity forecast: {activity_forecast}")
        
        # Test precision timing
        logger.info("Testing precision timing...")
        
        precise_time = time_manager.get_precise_time()
        logger.info(f"Precise time (microseconds): {precise_time}")
        
        # Test time synchronization
        logger.info("Testing time synchronization...")
        
        sync_status = await time_manager.check_time_synchronization()
        logger.info(f"Time synchronization status: {sync_status}")
        
        # Test historical time performance loading
        logger.info("Testing historical data integration...")
        
        # This would normally load from database
        historical_loaded = hasattr(time_manager, 'time_performance') and time_manager.time_performance
        logger.info(f"Historical time performance loaded: {historical_loaded}")
        
        if historical_loaded:
            logger.info(f"Time performance data points: {len(time_manager.time_performance)}")
        
        # Test real-time updates
        logger.info("Testing real-time updates...")
        
        # Start monitoring for a short period
        logger.info("Starting 5-second real-time monitoring...")
        
        for i in range(5):
            current_analysis = time_manager.analyze_market_timing()
            logger.info(f"Update {i+1}: {current_analysis}")
            await asyncio.sleep(1)
        
        logger.info("Enhanced Time Manager debug session completed successfully")
        
    except Exception as e:
        logger.error(f"Debug session failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        if 'db_manager' in locals():
            await db_manager.close()
            logger.info("Database connection closed")


if __name__ == "__main__":
    print("Enhanced Time Manager Debug Session")
    print("=" * 50)
    
    # Run debug session
    asyncio.run(debug_time_manager())
    
    print("=" * 50)
    print("Debug session completed")
