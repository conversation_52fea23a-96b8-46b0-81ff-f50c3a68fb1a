#!/usr/bin/env python3
"""
Quick MCP Status Check
"""

import json
import os
from pathlib import Path

def check_mcp_config():
    """Quick check of MCP configuration"""
    
    mcp_config_path = Path("C:/Users/<USER>/AppData/Roaming/Code - Insiders/User/mcp.json")
    
    print("="*60)
    print("MCP SYSTEM STATUS CHECK")
    print("="*60)
    
    if mcp_config_path.exists():
        print("✅ MCP configuration file found")
        
        try:
            with open(mcp_config_path, 'r') as f:
                config = json.load(f)
            
            print("✅ Configuration file is valid JSON")
            
            servers = config.get('mcpServers', {})
            print(f"📊 {len(servers)} MCP servers configured:")
            
            required_mcps = [
                'bybit-trading',
                'sequentialthinking', 
                'pylance',
                'memory',
                'postgres',
                'mssql'
            ]
            
            for mcp in required_mcps:
                if mcp in servers:
                    server_config = servers[mcp]
                    priority = server_config.get('settings', {}).get('priority', 'unknown')
                    copilot_enabled = server_config.get('settings', {}).get('copilotEnabled', False)
                    timeout = server_config.get('settings', {}).get('timeout', 0)
                    
                    status = "✅ CONFIGURED"
                    if mcp == 'bybit-trading' and priority == 'critical':
                        status += " (CRITICAL PRIORITY)"
                    
                    print(f"   {status}: {mcp}")
                    print(f"      Priority: {priority}, Timeout: {timeout}ms, Copilot: {'✅' if copilot_enabled else '❌'}")
                else:
                    print(f"   ❌ MISSING: {mcp}")
            
            # Check Copilot integration
            copilot_config = config.get('copilotIntegration', {})
            if copilot_config:
                print("\n🤖 COPILOT INTEGRATION:")
                print(f"   Context Sharing: {'✅' if copilot_config.get('enableContextSharing') else '❌'}")
                print(f"   Fast Response: {'✅' if copilot_config.get('fastResponseMode') else '❌'}")
                print(f"   Trading Optimized: {'✅' if copilot_config.get('tradingOptimized') else '❌'}")
                print(f"   Max Tokens: {copilot_config.get('maxContextTokens', 0)}")
                
                priority_servers = copilot_config.get('priorityServers', [])
                print(f"   Priority Servers: {', '.join(priority_servers)}")
                
                if 'bybit-trading' in priority_servers:
                    print("   ✅ Bybit trading is prioritized for Copilot")
                else:
                    print("   ⚠️ Bybit trading not in priority list")
            else:
                print("\n❌ COPILOT INTEGRATION: Not configured")
            
            # Summary
            configured_count = len([mcp for mcp in required_mcps if mcp in servers])
            print(f"\n📊 SUMMARY:")
            print(f"   Configured MCPs: {configured_count}/{len(required_mcps)}")
            
            if configured_count == len(required_mcps):
                print("   ✅ ALL REQUIRED MCPs CONFIGURED")
            else:
                missing = [mcp for mcp in required_mcps if mcp not in servers]
                print(f"   ⚠️ Missing MCPs: {', '.join(missing)}")
            
            # Critical trading MCP check
            if 'bybit-trading' in servers:
                bybit_config = servers['bybit-trading']
                if bybit_config.get('settings', {}).get('priority') == 'critical':
                    print("   ✅ BYBIT-TRADING MCP: CRITICAL PRIORITY CONFIRMED")
                else:
                    print("   ⚠️ BYBIT-TRADING MCP: Should be CRITICAL priority")
            else:
                print("   ❌ BYBIT-TRADING MCP: NOT CONFIGURED")
                
        except json.JSONDecodeError:
            print("❌ Configuration file contains invalid JSON")
        except Exception as e:
            print(f"❌ Error reading configuration: {e}")
    else:
        print("❌ MCP configuration file not found")
        print(f"   Expected location: {mcp_config_path}")
    
    print("="*60)

if __name__ == "__main__":
    check_mcp_config()
