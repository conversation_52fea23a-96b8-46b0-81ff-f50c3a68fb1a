#!/usr/bin/env python3
"""
Test import script to verify all critical system components can be imported
"""

import sys
import traceback

def test_import(module_name, class_name=None):
    """Test importing a module/class and report results"""
    try:
        if class_name:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {class_name} from {module_name} imported successfully")
            return True
        else:
            __import__(module_name)
            print(f"✅ {module_name} imported successfully")
            return True
    except Exception as e:
        print(f"❌ {class_name or module_name} import failed: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run comprehensive import tests"""
    print("🔍 Testing all critical imports...")
    print("=" * 60)
    
    import_results = []
    
    # Core components
    import_results.append(test_import("bybit_bot.core.config", "BotConfig"))
    import_results.append(test_import("bybit_bot.core.logger", "TradingBotLogger"))
    import_results.append(test_import("bybit_bot.database.connection", "DatabaseManager"))
    
    # AI components
    import_results.append(test_import("bybit_bot.ai.memory_manager", "PersistentMemoryManager"))
    import_results.append(test_import("bybit_bot.ai.meta_cognition_engine", "MetaCognitionEngine"))
    import_results.append(test_import("bybit_bot.ai.supergpt_integration", "SuperGPTIntegration"))
    
    # Exchange components
    import_results.append(test_import("bybit_bot.exchange.enhanced_bybit_client", "EnhancedBybitClient"))
    
    # Trading components
    import_results.append(test_import("bybit_bot.core.bot_manager", "BotManager"))
    
    # Strategy components
    import_results.append(test_import("bybit_bot.strategies.strategy_manager", "StrategyManager"))
    
    print("=" * 60)
    successful_imports = sum(import_results)
    total_imports = len(import_results)
    success_rate = (successful_imports / total_imports) * 100
    
    print(f"📊 Import Results: {successful_imports}/{total_imports} successful ({success_rate:.1f}%)")
    
    if successful_imports == total_imports:
        print("🎉 All imports successful! System ready for initialization.")
        return True
    else:
        print("⚠️ Some imports failed. Check dependencies and module structure.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
