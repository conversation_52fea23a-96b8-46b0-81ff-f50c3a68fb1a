#!/usr/bin/env python3
"""
MAIN ENTRY POINT - BYBIT TRADING BOT
Single entry point for the entire autonomous trading system

This file serves as the main entry point that imports and runs the unified system.
All functionality is contained in main_unified_system.py for better organization.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    # Import the unified system
    from main_unified_system import app, main as unified_main, unified_system
    
    # Export the FastAPI app for uvicorn
    __all__ = ['app']
    
    def main():
        """Main entry point that delegates to the unified system"""
        return unified_main()
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"Error importing unified system: {e}")
    print("Please ensure all dependencies are installed and the system is properly configured.")
    sys.exit(1)
