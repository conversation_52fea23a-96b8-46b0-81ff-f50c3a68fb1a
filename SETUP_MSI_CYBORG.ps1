# Development Environment Setup for MSI Cyborg 15 A12VF

## Quick Setup Script for Windows 11

```powershell
# Run as Administrator
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Install Chocolatey (if not already installed)
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}

# Install Node.js (optimized for 16GB RAM)
choco install nodejs --version=18.19.0 -y

# Install Git
choco install git -y

# Install Android Studio
choco install androidstudio -y

# Install Visual Studio Code
choco install vscode -y

# Configure Node.js for optimal performance
npm config set fund false
npm config set audit-level moderate
npm config set maxsockets 15

# Set up development environment variables
[Environment]::SetEnvironmentVariable("NODE_OPTIONS", "--max-old-space-size=8192", "User")
[Environment]::SetEnvironmentVariable("ANDROID_HOME", "$env:LOCALAPPDATA\Android\Sdk", "User")
[Environment]::SetEnvironmentVariable("JAVA_HOME", "${env:ProgramFiles}\Android\Android Studio\jre", "User")

Write-Host "✅ Development environment configured for MSI Cyborg 15 A12VF" -ForegroundColor Green
Write-Host "📱 Android development ready for Motorola Moto G32" -ForegroundColor Cyan
Write-Host "🔄 Please restart your terminal to apply changes" -ForegroundColor Yellow
```

## Performance Optimizations

### 1. Windows 11 Gaming Mode
```powershell
# Enable Game Mode for better development performance
Set-ItemProperty -Path "HKCU:\Software\Microsoft\GameBar" -Name "AllowAutoGameMode" -Value 1
Set-ItemProperty -Path "HKCU:\Software\Microsoft\GameBar" -Name "AutoGameModeEnabled" -Value 1
```

### 2. Hardware Acceleration
```powershell
# Enable hardware acceleration for better React Native performance
netsh int tcp set global autotuninglevel=normal
netsh int tcp set supplemental Internet congestionprovider=ctcp
```

### 3. Development Ports Configuration
```powershell
# Configure firewall rules for all development ports
$ports = @(3000, 8000, 8081, 19000, 19001, 19002)
foreach ($port in $ports) {
    New-NetFirewallRule -DisplayName "Dev Port $port" -Direction Inbound -LocalPort $port -Protocol TCP -Action Allow
}
```

### 4. Android Development Setup
```powershell
# Create Android SDK directories
$androidHome = "$env:LOCALAPPDATA\Android\Sdk"
New-Item -ItemType Directory -Force -Path $androidHome
New-Item -ItemType Directory -Force -Path "$androidHome\platform-tools"
New-Item -ItemType Directory -Force -Path "$androidHome\build-tools"

# Download platform tools (if needed)
# This will be handled by Android Studio
Write-Host "📱 Configure Android Studio to download SDK for API 33 (Android 13)" -ForegroundColor Cyan
```

## VS Code Extensions for Trading Bot Development

```powershell
# Install essential VS Code extensions
$extensions = @(
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "ms-python.python",
    "ms-vscode.powershell",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml"
)

foreach ($ext in $extensions) {
    code --install-extension $ext
}
```

## Network Configuration

### Local Development URLs
- **Frontend**: http://localhost:3000
- **Backend**: http://*************:8000
- **Metro Bundler**: http://localhost:8081

### Testing Commands
```powershell
# Test network connectivity
Test-NetConnection -ComputerName ************* -Port 8000 -InformationLevel Detailed

# Test local development server
Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing

# Check if ports are available
netstat -an | findstr ":3000"
netstat -an | findstr ":8000"
netstat -an | findstr ":8081"
```

## Build Scripts Optimized for MSI Hardware

### Frontend Build
```powershell
# Navigate to frontend directory
cd "e:\The_real_deal_copy\Bybit_Bot\BOT\frontend"

# Install dependencies with optimal settings
npm ci --prefer-offline --no-audit

# Development server with network access
npm run dev -- --host 0.0.0.0 --port 3000

# Production build (utilizes all 12 CPU cores)
npm run build
```

### Mobile Build for Moto G32
```powershell
# Navigate to mobile directory
cd "e:\The_real_deal_copy\Bybit_Bot\BOT\mobile"

# Install dependencies
npm ci --prefer-offline

# Start Metro bundler
npx react-native start --reset-cache

# Build APK for Motorola Moto G32 (in separate terminal)
npx react-native run-android --variant=release
```

## Monitoring & Debugging

### Performance Monitoring
```powershell
# Monitor system resources during development
while ($true) {
    Clear-Host
    Write-Host "🖥️  MSI Cyborg 15 A12VF Performance Monitor" -ForegroundColor Green
    Write-Host "=" * 50
    
    # CPU Usage
    $cpu = Get-Counter "\Processor(_Total)\% Processor Time" -SampleInterval 1 -MaxSamples 1
    Write-Host "💻 CPU Usage: $([math]::Round($cpu.CounterSamples.CookedValue, 2))%"
    
    # Memory Usage
    $memory = Get-CimInstance -ClassName CIM_OperatingSystem
    $usedMemory = [math]::Round(($memory.TotalVisibleMemorySize - $memory.FreePhysicalMemory) / 1MB, 2)
    $totalMemory = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
    Write-Host "🧠 Memory: ${usedMemory}GB / ${totalMemory}GB"
    
    # Development processes
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($nodeProcesses) {
        Write-Host "🟢 Node.js processes: $($nodeProcesses.Count)"
    }
    
    Start-Sleep -Seconds 2
}
```

## Quick Start Commands

```powershell
# Complete setup in one command
function Start-TradingBotDevelopment {
    Write-Host "🚀 Starting Trading Bot Development Environment" -ForegroundColor Green
    
    # Backend
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'e:\The_real_deal_copy\Bybit_Bot\BOT'; conda activate bybit-trader; python main.py"
    
    # Frontend
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'e:\The_real_deal_copy\Bybit_Bot\BOT\frontend'; npm run dev"
    
    # Mobile
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'e:\The_real_deal_copy\Bybit_Bot\BOT\mobile'; npx react-native start"
    
    Write-Host "✅ All development servers starting..." -ForegroundColor Cyan
    Write-Host "📱 Connect your Motorola Moto G32 to test the mobile app" -ForegroundColor Yellow
}

# Run the function
Start-TradingBotDevelopment
```
