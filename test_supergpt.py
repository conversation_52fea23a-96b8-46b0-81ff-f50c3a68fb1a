#!/usr/bin/env python3
"""
Quick SuperGPT Integration Test
"""

print("Testing SuperGPT Integration...")

try:
    from bybit_bot.ai.supergpt_integration import SuperGPTIntegration
    from bybit_bot.core.config import EnhancedBotConfig
    from bybit_bot.database.connection import DatabaseManager
    
    print("✅ Imports successful")
    
    # Get proper config and database for SuperGPT
    config = EnhancedBotConfig()
    print("✅ Config initialized")
    
    db_manager = DatabaseManager()
    print("✅ Database manager initialized")
    
    # Initialize SuperGPT with proper parameters
    supergpt = SuperGPTIntegration(bot_config=config, database_manager=db_manager)
    print("✅ SuperGPT Integration: OPERATIONAL")
    
    # Test capabilities
    status = supergpt.get_system_status()
    print(f"✅ SuperGPT Status: {status['initialized']}")
    print(f"✅ Active Capabilities: {len([k for k, v in status['active_capabilities'].items() if v])}")
    
except Exception as e:
    print(f"❌ SuperGPT Integration Test Failed: {e}")
    import traceback
    traceback.print_exc()

print("SuperGPT Integration Test Complete")
