#!/usr/bin/env python3
"""
Debug script for Data Crawler
Tests and debugs the data collection and processing system
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger, setup_logging
from bybit_bot.data_crawler.market_data_crawler import MarketDataCrawler as DataCrawler
from bybit_bot.database.connection import DatabaseManager


async def debug_data_crawler():
    """Debug the data crawler"""
    
    # Setup logging
    setup_logging(log_level="DEBUG", console_output=True)
    logger = TradingBotLogger("DebugDataCrawler")
    
    logger.info("Starting Data Crawler Debug Session")
    
    try:
        # Initialize configuration
        config = BotConfig()
        logger.info("Configuration loaded successfully")
        
        # Initialize database
        db_manager = DatabaseManager(config)
        await db_manager.connect()
        logger.info("Database connected")
        
        # Initialize data crawler
        crawler = DataCrawler(config, db_manager)
        await crawler.initialize()
        logger.info("Data Crawler initialized")
        
        # Test symbol data collection
        logger.info("Testing symbol data collection...")
        symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
        
        for symbol in symbols:
            logger.info(f"Testing data collection for {symbol}")
            
            # Test market data collection
            market_data = await crawler.collect_market_data(symbol)
            logger.info(f"Market data for {symbol}: {len(market_data) if market_data else 0} records")
            
            # Test orderbook data
            orderbook_data = await crawler.collect_orderbook_data(symbol)
            logger.info(f"Orderbook data for {symbol}: {orderbook_data is not None}")
            
            # Test trade data
            trade_data = await crawler.collect_trade_data(symbol)
            logger.info(f"Trade data for {symbol}: {len(trade_data) if trade_data else 0} records")
        
        # Test news data collection
        logger.info("Testing news data collection...")
        news_data = await crawler.collect_news_data()
        logger.info(f"News data collected: {len(news_data) if news_data else 0} articles")
        
        # Test social sentiment collection
        logger.info("Testing social sentiment collection...")
        sentiment_data = await crawler.collect_social_sentiment()
        logger.info(f"Sentiment data collected: {len(sentiment_data) if sentiment_data else 0} records")
        
        # Test economic data collection
        logger.info("Testing economic data collection...")
        economic_data = await crawler.collect_economic_data()
        logger.info(f"Economic data collected: {len(economic_data) if economic_data else 0} records")
        
        # Test data storage
        logger.info("Testing data storage...")
        if market_data:
            storage_result = await crawler.store_market_data(market_data, 'BTCUSDT')
            logger.info(f"Data storage result: {storage_result}")
        
        # Test data validation
        logger.info("Testing data validation...")
        validation_result = await crawler.validate_data_quality()
        logger.info(f"Data validation result: {validation_result}")
        
        # Test data cleanup
        logger.info("Testing data cleanup...")
        cleanup_result = await crawler.cleanup_old_data()
        logger.info(f"Data cleanup result: {cleanup_result}")
        
        # Test real-time data streaming
        logger.info("Testing real-time data streaming...")
        stream_test_duration = 10  # seconds
        logger.info(f"Starting {stream_test_duration}s real-time data test...")
        
        # Start streaming for a short period
        streaming_task = asyncio.create_task(crawler.start_realtime_streaming(['BTCUSDT']))
        await asyncio.sleep(stream_test_duration)
        streaming_task.cancel()
        
        try:
            await streaming_task
        except asyncio.CancelledError:
            logger.info("Real-time streaming test completed")
        
        # Test data statistics
        logger.info("Testing data statistics...")
        stats = await crawler.get_data_statistics()
        logger.info(f"Data statistics: {stats}")
        
        logger.info("Data Crawler debug session completed successfully")
        
    except Exception as e:
        logger.error(f"Debug session failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        if 'crawler' in locals():
            await crawler.shutdown()
            logger.info("Data crawler shutdown")
        
        if 'db_manager' in locals():
            await db_manager.close()
            logger.info("Database connection closed")


if __name__ == "__main__":
    print("Data Crawler Debug Session")
    print("=" * 50)
    
    # Run debug session
    asyncio.run(debug_data_crawler())
    
    print("=" * 50)
    print("Debug session completed")
