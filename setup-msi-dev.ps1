# MSI Cyborg 15 A12VF Development Environment Setup
# Optimized for Intel Core i5-12450H with 16GB RAM

Write-Host "🖥️  Setting up development environment for MSI Cyborg 15 A12VF..." -ForegroundColor Green
Write-Host "⚡ Intel Core i5-12450H | 16GB RAM | Windows 11 Home" -ForegroundColor Cyan

# Check system requirements
Write-Host "🔍 Checking system requirements..." -ForegroundColor Yellow

# Check Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js 18+" -ForegroundColor Red
}

# Check npm
try {
    $npmVersion = npm --version
    Write-Host "✅ npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm not found" -ForegroundColor Red
}

# Check Python (for React Native)
try {
    $pythonVersion = python --version
    Write-Host "✅ Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Python not found - needed for React Native" -ForegroundColor Yellow
}

# Check if Android Studio is installed
$androidStudioPath = "C:\Program Files\Android\Android Studio"
if (Test-Path $androidStudioPath) {
    Write-Host "✅ Android Studio found" -ForegroundColor Green
} else {
    Write-Host "⚠️  Android Studio not found at $androidStudioPath" -ForegroundColor Yellow
    Write-Host "Please install Android Studio for mobile development" -ForegroundColor Yellow
}

# Optimize for your hardware
Write-Host ""
Write-Host "🚀 Optimizing for your MSI Cyborg 15 hardware..." -ForegroundColor Green

# Set Node.js memory limit for your 16GB RAM
$env:NODE_OPTIONS = "--max_old_space_size=8192"
Write-Host "💾 Set Node.js memory limit to 8GB (optimal for 16GB system)" -ForegroundColor Cyan

# Enable performance mode for Intel CPU
Write-Host "⚡ Enabling high-performance mode..." -ForegroundColor Yellow
powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c

# Create optimized Vite config for your system
Write-Host "⚙️  Creating optimized Vite configuration..." -ForegroundColor Yellow

$viteConfig = @"
// Optimized for MSI Cyborg 15 A12VF
export default {
  server: {
    host: '0.0.0.0',
    port: 3000,
    hmr: {
      port: 3001,
    },
  },
  build: {
    // Optimize for Intel i5-12450H
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@mui/material', '@mui/icons-material'],
          charts: ['recharts'],
        },
      },
    },
    // Use all available cores
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    // Optimize chunk size for better loading
    chunkSizeWarningLimit: 1000,
  },
  optimizeDeps: {
    // Pre-bundle dependencies for faster dev server
    include: [
      'react',
      'react-dom',
      '@mui/material',
      '@mui/icons-material',
      'recharts',
      'framer-motion',
    ],
  },
}
"@

$viteConfig | Out-File -FilePath "..\frontend\vite.config.optimized.js" -Encoding UTF8

Write-Host "📁 Created optimized Vite config at frontend\vite.config.optimized.js" -ForegroundColor Cyan

Write-Host ""
Write-Host "🎯 DEVELOPMENT RECOMMENDATIONS FOR YOUR MSI LAPTOP:" -ForegroundColor Yellow
Write-Host "1. Use Windows Terminal for better performance" -ForegroundColor White
Write-Host "2. Close unnecessary applications during builds" -ForegroundColor White
Write-Host "3. Enable Game Mode for maximum CPU performance" -ForegroundColor White
Write-Host "4. Use SSD for project files (if available)" -ForegroundColor White
Write-Host "5. Enable hardware acceleration in VS Code" -ForegroundColor White

Write-Host ""
Write-Host "⚡ MSI Cyborg 15 optimization complete!" -ForegroundColor Green
