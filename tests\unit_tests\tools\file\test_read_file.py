#!/usr/bin/env python3
"""
Real File Reading Test - NO MOCK DATA
Tests actual file reading operations with real system files
"""

import pytest
from pathlib import Path

def test_read_real_config_file():
    """Test reading real configuration file"""
    project_root = Path(__file__).parent.parent.parent.parent
    config_template = project_root / "config_template.yaml"
    
    if config_template.exists():
        with open(config_template, 'r', encoding='utf-8') as f:
            content = f.read()
        
        assert len(content) > 0
        assert 'bybit' in content.lower() or 'trading' in content.lower()

def test_read_real_requirements():
    """Test reading real requirements file"""
    project_root = Path(__file__).parent.parent.parent.parent
    requirements = project_root / "requirements.txt"
    
    if requirements.exists():
        with open(requirements, 'r', encoding='utf-8') as f:
            content = f.read()
        
        assert len(content) > 0
        # Check for real dependencies
        dependencies = ['fastapi', 'pandas', 'numpy', 'requests']
        found_deps = [dep for dep in dependencies if dep in content.lower()]
        assert len(found_deps) > 0

def test_read_real_python_file():
    """Test reading real Python file"""
    project_root = Path(__file__).parent.parent.parent.parent
    main_file = project_root / "main.py"
    
    if main_file.exists():
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        assert len(content) > 0
        assert 'python' in content.lower() or 'import' in content

def test_read_real_system_files():
    """Test reading real system configuration files"""
    project_root = Path(__file__).parent.parent.parent.parent
    
    # Test various real files
    test_files = [
        'main.py',
        'requirements.txt', 
        'README.md',
        'config_template.yaml'
    ]
    
    found_files = 0
    for filename in test_files:
        file_path = project_root / filename
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            assert len(content) > 0
            found_files += 1
    
    assert found_files > 0, "No real system files found to test"


