"""
Enhanced Time Management System with Market Session Awareness
Implements precise timing, calendar awareness, and market activity optimization
"""

import asyncio
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from zoneinfo import ZoneInfo
import holidays
import calendar

from .config import BotConfig
from .logger import TradingBotLogger
from ..database.connection import DatabaseManager


class MarketRegion(Enum):
    """Global market regions"""
    ASIA_PACIFIC = "asia_pacific"
    EUROPE = "europe" 
    NORTH_AMERICA = "north_america"
    GLOBAL = "global"


class TradingDay(Enum):
    """Trading day classifications"""
    WEEKDAY = "weekday"
    WEEKEND = "weekend"
    HOLIDAY = "holiday"
    HALF_DAY = "half_day"
    SPECIAL_EVENT = "special_event"


@dataclass
class PreciseTimeMetrics:
    """Ultra-precise time metrics for trading decisions"""
    utc_timestamp: float  # Unix timestamp with microsecond precision
    utc_datetime: datetime
    local_datetime: datetime
    market_session: str
    trading_day_type: TradingDay
    milliseconds_in_session: int
    microseconds_precision: int
    time_zone_offset: float
    daylight_saving: bool
    market_open_countdown: Optional[timedelta]
    market_close_countdown: Optional[timedelta]
    optimal_trading_window: bool
    volatility_expected: float
    volume_expected: float


@dataclass
class MarketCalendar:
    """Market calendar with events and schedules"""
    date: datetime
    is_trading_day: bool
    market_open: Optional[datetime]
    market_close: Optional[datetime]
    half_day: bool
    holiday_name: Optional[str]
    special_events: List[str]
    expected_volatility: float
    expected_volume: float


class EnhancedTimeManager:
    """
    Ultra-precise time management system providing:
    - Microsecond precision timing
    - Global market session coordination
    - Holiday and event calendar integration
    - Optimal trading window detection
    - Time-based strategy optimization
    - Market rhythm analysis
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db = database_manager
        self.logger = TradingBotLogger(config)
        
        # Time zones for major markets
        self.time_zones = {
            'UTC': timezone.utc,
            'Tokyo': ZoneInfo("Asia/Tokyo"),
            'Sydney': ZoneInfo("Australia/Sydney"),
            'London': ZoneInfo("Europe/London"),
            'Frankfurt': ZoneInfo("Europe/Berlin"),
            'New_York': ZoneInfo("America/New_York"),
            'Chicago': ZoneInfo("America/Chicago"),
            'Los_Angeles': ZoneInfo("America/Los_Angeles")
        }
        
        # Market sessions with precise timing
        self.market_sessions = {
            'Asian_Early': {'start': 21, 'end': 2, 'peak': 23},  # UTC hours
            'Asian_Main': {'start': 0, 'end': 9, 'peak': 4},
            'European_Pre': {'start': 6, 'end': 8, 'peak': 7},
            'European_Main': {'start': 7, 'end': 16, 'peak': 10},
            'US_Pre': {'start': 12, 'end': 14, 'peak': 13},
            'US_Main': {'start': 13, 'end': 22, 'peak': 16},
            'Overlap_Asia_Europe': {'start': 7, 'end': 9, 'peak': 8},
            'Overlap_Europe_US': {'start': 13, 'end': 16, 'peak': 14}
        }
        
        # Holiday calendars for major markets
        self.holiday_calendars = {
            'US': holidays.UnitedStates(),
            'UK': holidays.UnitedKingdom(),
            'Japan': holidays.Japan(),
            'Germany': holidays.Germany(),
            'Australia': holidays.Australia(),
            'China': holidays.China(),
            'Hong_Kong': holidays.HongKong(),
            'Singapore': holidays.Singapore()
        }
        
        # Market activity profiles by time
        self.activity_profiles = {}
        self._initialize_activity_profiles()
        
        # Position timing tracking
        self.position_timings = {}
        self.session_statistics = {}
        
        # Performance by time periods
        self.time_performance = {}
        
    async def initialize(self):
        """Initialize the enhanced time manager"""
        try:
            self.logger.info("🕰️ Initializing Enhanced Time Manager with microsecond precision...")
            
            # Load historical time-based performance data
            await self._load_historical_time_performance()
            
            # Initialize market calendar
            await self._build_market_calendar()
            
            # Load session statistics
            await self._load_session_statistics()
            
            self.logger.info("✅ Enhanced Time Manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Enhanced Time Manager: {e}")
            raise
    
    def get_precise_time_metrics(self) -> PreciseTimeMetrics:
        """Get ultra-precise time metrics for trading decisions"""
        try:
            # Get current time with maximum precision
            utc_timestamp = time.time()
            utc_datetime = datetime.fromtimestamp(utc_timestamp, tz=timezone.utc)
            
            # Calculate microseconds
            microseconds = int((utc_timestamp % 1) * 1_000_000)
            
            # Determine current market session
            current_session = self._get_current_market_session(utc_datetime)
            
            # Calculate time in session
            session_start = self._get_session_start_time(current_session, utc_datetime)
            milliseconds_in_session = int((utc_datetime - session_start).total_seconds() * 1000)
            
            # Get trading day classification
            trading_day_type = self._get_trading_day_type(utc_datetime)
            
            # Calculate market timing
            market_open_countdown = self._get_time_to_market_open(utc_datetime)
            market_close_countdown = self._get_time_to_market_close(utc_datetime)
            
            # Determine if in optimal trading window
            optimal_window = self._is_optimal_trading_window(utc_datetime, current_session)
            
            # Get expected market activity
            volatility_expected = self._get_expected_volatility(utc_datetime, current_session)
            volume_expected = self._get_expected_volume(utc_datetime, current_session)
            
            return PreciseTimeMetrics(
                utc_timestamp=utc_timestamp,
                utc_datetime=utc_datetime,
                local_datetime=utc_datetime.astimezone(),
                market_session=current_session,
                trading_day_type=trading_day_type,
                milliseconds_in_session=milliseconds_in_session,
                microseconds_precision=microseconds,
                time_zone_offset=utc_datetime.astimezone().utcoffset().total_seconds() / 3600,
                daylight_saving=time.daylight > 0,
                market_open_countdown=market_open_countdown,
                market_close_countdown=market_close_countdown,
                optimal_trading_window=optimal_window,
                volatility_expected=volatility_expected,
                volume_expected=volume_expected
            )
            
        except Exception as e:
            self.logger.error(f"Error getting precise time metrics: {e}")
            raise
    
    def get_market_rhythm_analysis(self, lookback_days: int = 30) -> Dict[str, Any]:
        """Analyze market rhythm patterns over time"""
        try:
            now = datetime.utcnow()
            analysis = {
                'timestamp': now,
                'lookback_days': lookback_days,
                'hourly_patterns': {},
                'daily_patterns': {},
                'weekly_patterns': {},
                'monthly_patterns': {},
                'optimal_windows': [],
                'high_volatility_periods': [],
                'low_activity_periods': []
            }
            
            # Analyze hourly patterns (UTC)
            for hour in range(24):
                analysis['hourly_patterns'][hour] = {
                    'avg_volatility': self._get_historical_avg_volatility(hour),
                    'avg_volume': self._get_historical_avg_volume(hour),
                    'success_rate': self._get_historical_success_rate(hour),
                    'optimal_for_trading': hour in self._get_optimal_hours()
                }
            
            # Analyze daily patterns
            for day in range(7):  # 0=Monday, 6=Sunday
                day_name = calendar.day_name[day]
                analysis['daily_patterns'][day_name] = {
                    'avg_volatility': self._get_daily_avg_volatility(day),
                    'avg_volume': self._get_daily_avg_volume(day),
                    'market_open': day < 5,  # Weekdays
                    'recommended_strategy': self._get_recommended_daily_strategy(day)
                }
            
            # Find optimal trading windows
            analysis['optimal_windows'] = self._identify_optimal_windows()
            
            # Identify high volatility periods
            analysis['high_volatility_periods'] = self._identify_high_volatility_periods()
            
            # Identify low activity periods to avoid
            analysis['low_activity_periods'] = self._identify_low_activity_periods()
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing market rhythm: {e}")
            return {}
    
    def calculate_position_timing_score(self, 
                                      entry_time: datetime, 
                                      exit_time: Optional[datetime] = None) -> Dict[str, Any]:
        """Calculate timing score for position entry/exit"""
        try:
            if exit_time is None:
                exit_time = datetime.utcnow()
            
            # Calculate holding duration
            duration = exit_time - entry_time
            duration_hours = duration.total_seconds() / 3600
            
            # Get session information for entry and exit
            entry_session = self._get_current_market_session(entry_time)
            exit_session = self._get_current_market_session(exit_time)
            
            # Calculate timing scores
            entry_score = self._calculate_timing_score(entry_time, entry_session)
            exit_score = self._calculate_timing_score(exit_time, exit_session)
            
            # Overall timing assessment
            overall_score = (entry_score + exit_score) / 2
            
            return {
                'entry_time': entry_time,
                'exit_time': exit_time,
                'duration_hours': duration_hours,
                'entry_session': entry_session,
                'exit_session': exit_session,
                'entry_score': entry_score,
                'exit_score': exit_score,
                'overall_score': overall_score,
                'timing_grade': self._get_timing_grade(overall_score),
                'recommendations': self._get_timing_recommendations(overall_score)
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating position timing score: {e}")
            return {}
    
    def get_next_optimal_entry_window(self, hours_ahead: int = 24) -> List[Dict[str, Any]]:
        """Get next optimal entry windows within specified hours"""
        try:
            now = datetime.utcnow()
            end_time = now + timedelta(hours=hours_ahead)
            optimal_windows = []
            
            # Check each hour for optimal conditions
            current = now
            while current < end_time:
                hour = current.hour
                day_of_week = current.weekday()
                
                # Check if this hour is historically optimal
                if self._is_historically_optimal_hour(hour, day_of_week):
                    session = self._get_current_market_session(current)
                    
                    window = {
                        'start_time': current,
                        'end_time': current + timedelta(hours=1),
                        'session': session,
                        'expected_volatility': self._get_expected_volatility(current, session),
                        'expected_volume': self._get_expected_volume(current, session),
                        'confidence_score': self._calculate_window_confidence(current),
                        'recommended_strategies': self._get_recommended_strategies_for_time(current)
                    }
                    optimal_windows.append(window)
                
                current += timedelta(hours=1)
            
            # Sort by confidence score
            optimal_windows.sort(key=lambda x: x['confidence_score'], reverse=True)
            
            return optimal_windows[:10]  # Top 10 windows
            
        except Exception as e:
            self.logger.error(f"Error getting optimal entry windows: {e}")
            return []
    
    def should_trade_now(self, strategy_type: str = 'general') -> Dict[str, Any]:
        """Comprehensive analysis of whether to trade now"""
        try:
            time_metrics = self.get_precise_time_metrics()
            
            # Base decision factors
            factors = {
                'market_open': time_metrics.trading_day_type == TradingDay.WEEKDAY,
                'optimal_window': time_metrics.optimal_trading_window,
                'sufficient_volatility': time_metrics.volatility_expected > 0.5,
                'sufficient_volume': time_metrics.volume_expected > 0.5,
                'not_holiday': time_metrics.trading_day_type != TradingDay.HOLIDAY,
                'session_active': time_metrics.market_session != 'Quiet'
            }
            
            # Calculate overall score
            positive_factors = sum(1 for factor in factors.values() if factor)
            total_factors = len(factors)
            confidence_score = positive_factors / total_factors
            
            # Strategy-specific adjustments
            if strategy_type == 'scalping':
                # Scalping needs high volatility and volume
                if time_metrics.volatility_expected < 0.7 or time_metrics.volume_expected < 0.7:
                    confidence_score *= 0.5
            elif strategy_type == 'swing':
                # Swing trading is less time-sensitive
                confidence_score += 0.1
            elif strategy_type == 'news':
                # News trading needs to consider event timing
                if self._is_near_major_news_time(time_metrics.utc_datetime):
                    confidence_score += 0.2
            
            # Final recommendation
            should_trade = confidence_score > 0.6
            
            return {
                'should_trade': should_trade,
                'confidence_score': confidence_score,
                'factors': factors,
                'time_metrics': time_metrics,
                'recommendation': 'TRADE' if should_trade else 'WAIT',
                'next_optimal_window': self.get_next_optimal_entry_window(6)[0] if not should_trade else None
            }
            
        except Exception as e:
            self.logger.error(f"Error determining if should trade now: {e}")
            return {'should_trade': False, 'confidence_score': 0.0}
    
    # Private helper methods
    def _initialize_activity_profiles(self):
        """Initialize market activity profiles using real session data"""
        # Initialize with empty profiles - will be populated from real data
        self.activity_profiles = {}

        # Define session hour ranges
        session_hours = {
            'Asian_Early': list(range(21, 24)) + list(range(0, 2)),
            'Asian_Main': list(range(0, 9)),
            'European_Pre': list(range(6, 8)),
            'European_Main': list(range(7, 16)),
            'US_Pre': list(range(12, 14)),
            'US_Main': list(range(13, 22)),
            'Overlap_Asia_Europe': list(range(7, 9)),
            'Overlap_Europe_US': list(range(13, 16))
        }

        # Calculate profiles from real session statistics if available
        for session_name in session_hours.keys():
            if session_name in self.session_statistics:
                # Use real session data
                stats = self.session_statistics[session_name]
                self.activity_profiles[session_name] = {
                    'volatility': stats['avg_volatility'],
                    'volume': stats['avg_volume'],
                    'spread': stats['avg_spread']
                }
            else:
                # No real data available - set to zero (no trading)
                self.activity_profiles[session_name] = {
                    'volatility': 0.0,
                    'volume': 0.0,
                    'spread': 0.0
                }
    
    def _get_current_market_session(self, dt: datetime) -> str:
        """Determine current market session"""
        hour = dt.hour
        
        # Check for overlap sessions first (highest priority)
        if 7 <= hour < 9:
            return 'Overlap_Asia_Europe'
        elif 13 <= hour < 16:
            return 'Overlap_Europe_US'
        
        # Individual sessions
        elif 21 <= hour or hour < 2:
            return 'Asian_Early'
        elif 0 <= hour < 9:
            return 'Asian_Main'
        elif 6 <= hour < 8:
            return 'European_Pre'
        elif 7 <= hour < 16:
            return 'European_Main'
        elif 12 <= hour < 14:
            return 'US_Pre'
        elif 13 <= hour < 22:
            return 'US_Main'
        else:
            return 'Quiet'
    
    def _get_trading_day_type(self, dt: datetime) -> TradingDay:
        """Classify the type of trading day"""
        # Check if weekend
        if dt.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return TradingDay.WEEKEND
        
        # Check for holidays
        date = dt.date()
        for holiday_calendar in self.holiday_calendars.values():
            if date in holiday_calendar:
                return TradingDay.HOLIDAY
        
        return TradingDay.WEEKDAY
    
    def _get_session_start_time(self, session: str, dt: datetime) -> datetime:
        """Get the start time of a session"""
        if session in self.market_sessions:
            start_hour = self.market_sessions[session]['start']
            return dt.replace(hour=start_hour, minute=0, second=0, microsecond=0)
        return dt
    
    def _get_time_to_market_open(self, dt: datetime) -> Optional[timedelta]:
        """Get time until next market open"""
        if dt.weekday() >= 5:  # Weekend
            # Next market open is Monday
            days_until_monday = (7 - dt.weekday()) % 7
            if days_until_monday == 0:
                days_until_monday = 1
            next_open = dt.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=days_until_monday)
            return next_open - dt
        
        # During weekday, find next session start
        current_hour = dt.hour
        next_session_hour = None
        
        for times in self.market_sessions.values():
            start_hour = times['start']
            if start_hour > current_hour:
                next_session_hour = start_hour
                break
        
        if next_session_hour:
            next_open = dt.replace(hour=next_session_hour, minute=0, second=0, microsecond=0)
            return next_open - dt
        
        return None
    
    def _get_time_to_market_close(self, dt: datetime) -> Optional[timedelta]:
        """Get time until current session closes"""
        current_session = self._get_current_market_session(dt)
        if current_session in self.market_sessions:
            end_hour = self.market_sessions[current_session]['end']
            end_time = dt.replace(hour=end_hour, minute=0, second=0, microsecond=0)
            if end_time < dt:
                end_time += timedelta(days=1)
            return end_time - dt
        return None
    
    def _is_optimal_trading_window(self, dt: datetime, session: str) -> bool:
        """Check if current time is in optimal trading window"""
        if session == 'Quiet':
            return False
        
        hour = dt.hour
        
        # Peak hours for each session
        peak_hours = {
            'Asian_Main': [3, 4, 5],
            'European_Main': [9, 10, 11, 14, 15],
            'US_Main': [15, 16, 17, 20, 21],
            'Overlap_Asia_Europe': [7, 8],
            'Overlap_Europe_US': [13, 14, 15]
        }
        
        if session in peak_hours:
            return hour in peak_hours[session]
        
        return False
    
    def _get_expected_volatility(self, dt: datetime, session: str) -> float:
        """Get expected volatility for time period"""
        base_volatility = self.activity_profiles.get(session, {}).get('volatility', 0.5)
        
        # Adjust for specific conditions
        if dt.weekday() >= 5:  # Weekend
            base_volatility *= 0.3
        elif self._get_trading_day_type(dt) == TradingDay.HOLIDAY:
            base_volatility *= 0.2
        
        return min(1.0, base_volatility)
    
    def _get_expected_volume(self, dt: datetime, session: str) -> float:
        """Get expected volume for time period"""
        base_volume = self.activity_profiles.get(session, {}).get('volume', 0.5)
        
        # Adjust for specific conditions
        if dt.weekday() >= 5:  # Weekend
            base_volume *= 0.2
        elif self._get_trading_day_type(dt) == TradingDay.HOLIDAY:
            base_volume *= 0.1
        
        return min(1.0, base_volume)
    
    async def _load_historical_time_performance(self):
        """Load historical performance by time periods"""
        try:
            # Load historical performance data from database
            if self.db:
                query = """
                SELECT
                    EXTRACT(hour FROM timestamp) as hour,
                    EXTRACT(dow FROM timestamp) as day_of_week,
                    AVG(profit_loss) as avg_profit,
                    COUNT(*) as trade_count,
                    AVG(CASE WHEN profit_loss > 0 THEN 1 ELSE 0 END) as success_rate
                FROM trades
                WHERE timestamp > NOW() - INTERVAL '90 days'
                GROUP BY hour, day_of_week
                """

                results = await self.db.fetch_all(query)

                # Process results into time_performance structure
                self.time_performance = {}
                for row in results:
                    hour = int(row['hour'])
                    day = int(row['day_of_week'])
                    key = f"{day}_{hour}"

                    self.time_performance[key] = {
                        'avg_profit': float(row['avg_profit']) if row['avg_profit'] else 0.0,
                        'trade_count': int(row['trade_count']),
                        'success_rate': float(row['success_rate']) if row['success_rate'] else 0.5
                    }
            else:
                # Fallback to default performance data
                self.time_performance = {}

        except Exception as e:
            self.logger.error(f"Error loading historical time performance: {e}")
            self.time_performance = {}
    
    async def _build_market_calendar(self):
        """Build comprehensive market calendar"""
        try:
            # Build market calendar for next 365 days
            self.market_calendar = {}

            start_date = datetime.now(timezone.utc).date()
            for i in range(365):
                current_date = start_date + timedelta(days=i)

                # Check if it's a trading day
                is_trading_day = current_date.weekday() < 5  # Monday=0, Friday=4

                # Check for holidays
                holiday_name = None
                for holiday_calendar in self.holiday_calendars.values():
                    if current_date in holiday_calendar:
                        holiday_name = holiday_calendar.get(current_date)
                        is_trading_day = False
                        break

                # Determine market hours (crypto markets are 24/7, but activity varies)
                market_open = datetime.combine(current_date, datetime.min.time()).replace(tzinfo=timezone.utc)
                market_close = market_open + timedelta(hours=24)

                # Calculate expected activity based on day type
                if is_trading_day:
                    expected_volatility = 1.0
                    expected_volume = 1.0
                else:
                    expected_volatility = 0.3
                    expected_volume = 0.2

                # Adjust for specific days
                if current_date.weekday() == 0:  # Monday
                    expected_volatility *= 1.2
                    expected_volume *= 1.1
                elif current_date.weekday() == 4:  # Friday
                    expected_volatility *= 0.9
                    expected_volume *= 0.8

                calendar_entry = MarketCalendar(
                    date=datetime.combine(current_date, datetime.min.time()).replace(tzinfo=timezone.utc),
                    is_trading_day=is_trading_day,
                    market_open=market_open,
                    market_close=market_close,
                    half_day=False,  # Crypto doesn't have half days
                    holiday_name=holiday_name,
                    special_events=[],
                    expected_volatility=expected_volatility,
                    expected_volume=expected_volume
                )

                self.market_calendar[current_date.isoformat()] = calendar_entry

        except Exception as e:
            self.logger.error(f"Error building market calendar: {e}")
    
    async def _load_session_statistics(self):
        """Load session-based statistics"""
        try:
            # Load session statistics from database
            if self.db:
                query = """
                SELECT
                    session_name,
                    AVG(volatility) as avg_volatility,
                    AVG(volume) as avg_volume,
                    AVG(spread) as avg_spread,
                    COUNT(*) as sample_count
                FROM session_statistics
                WHERE timestamp > NOW() - INTERVAL '30 days'
                GROUP BY session_name
                """

                results = await self.db.fetch_all(query)

                # Process results
                self.session_statistics = {}
                for row in results:
                    session_name = row['session_name']
                    self.session_statistics[session_name] = {
                        'avg_volatility': float(row['avg_volatility']) if row['avg_volatility'] else 0.5,
                        'avg_volume': float(row['avg_volume']) if row['avg_volume'] else 0.5,
                        'avg_spread': float(row['avg_spread']) if row['avg_spread'] else 1.0,
                        'sample_count': int(row['sample_count'])
                    }
            else:
                # Fallback to default session statistics
                self.session_statistics = {}

        except Exception as e:
            self.logger.error(f"Error loading session statistics: {e}")
            self.session_statistics = {}
    
    def _get_historical_avg_volatility(self, hour: int) -> float:
        """Get historical average volatility for hour"""
        # Try to get from loaded time performance data first
        current_day = datetime.now(timezone.utc).weekday()
        key = f"{current_day}_{hour}"

        if key in self.time_performance and self.time_performance[key]['trade_count'] > 0:
            # Calculate volatility from real trade data
            avg_profit = abs(self.time_performance[key]['avg_profit'])
            trade_count = self.time_performance[key]['trade_count']
            success_rate = self.time_performance[key]['success_rate']

            # Calculate volatility based on real performance metrics
            volatility = min(1.0, (avg_profit * 10) + (success_rate * 0.5) + (min(trade_count, 100) / 100))
            return max(0.0, volatility)

        # No real data available - return 0 (no trading recommended without data)
        return 0.0
    
    def _get_historical_avg_volume(self, hour: int) -> float:
        """Get historical average volume for hour using real data only"""
        current_day = datetime.now(timezone.utc).weekday()
        key = f"{current_day}_{hour}"

        if key in self.time_performance and self.time_performance[key]['trade_count'] > 0:
            # Use trade count as volume indicator
            trade_count = self.time_performance[key]['trade_count']
            # Normalize to 0-1 scale based on maximum observed trades per hour
            max_trades_per_hour = 100  # Will be updated dynamically
            volume = min(1.0, trade_count / max_trades_per_hour)
            return volume

        # No real data available - return 0
        return 0.0
    
    def _get_historical_success_rate(self, hour: int) -> float:
        """Get historical success rate for hour using real data only"""
        current_day = datetime.now(timezone.utc).weekday()
        key = f"{current_day}_{hour}"

        if key in self.time_performance and self.time_performance[key]['trade_count'] > 0:
            return self.time_performance[key]['success_rate']

        # No real data available - return 0
        return 0.0
    
    def _get_optimal_hours(self) -> List[int]:
        """Get list of optimal trading hours based on real performance data"""
        optimal_hours = []

        # Analyze all hours and find those with positive performance
        for hour in range(24):
            current_day = datetime.now(timezone.utc).weekday()
            key = f"{current_day}_{hour}"

            if key in self.time_performance:
                perf_data = self.time_performance[key]
                # Consider hour optimal if it has good success rate and positive profit
                if (perf_data['success_rate'] > 0.6 and
                    perf_data['avg_profit'] > 0 and
                    perf_data['trade_count'] > 5):
                    optimal_hours.append(hour)

        return optimal_hours
    
    def _get_daily_avg_volatility(self, day: int) -> float:
        """Get average volatility for day of week using real data only"""
        total_volatility = 0.0
        hour_count = 0

        # Calculate average volatility across all hours for this day
        for hour in range(24):
            key = f"{day}_{hour}"
            if key in self.time_performance and self.time_performance[key]['trade_count'] > 0:
                avg_profit = abs(self.time_performance[key]['avg_profit'])
                success_rate = self.time_performance[key]['success_rate']
                volatility = min(1.0, (avg_profit * 10) + (success_rate * 0.5))
                total_volatility += volatility
                hour_count += 1

        return total_volatility / hour_count if hour_count > 0 else 0.0

    def _get_daily_avg_volume(self, day: int) -> float:
        """Get average volume for day of week using real data only"""
        total_volume = 0.0
        hour_count = 0

        # Calculate average volume across all hours for this day
        for hour in range(24):
            key = f"{day}_{hour}"
            if key in self.time_performance and self.time_performance[key]['trade_count'] > 0:
                trade_count = self.time_performance[key]['trade_count']
                volume = min(1.0, trade_count / 100)  # Normalize
                total_volume += volume
                hour_count += 1

        return total_volume / hour_count if hour_count > 0 else 0.0
    
    def _get_recommended_daily_strategy(self, day: int) -> str:
        """Get recommended strategy for day of week"""
        if day < 5:  # Weekdays
            return 'active_trading'
        else:  # Weekend
            return 'position_management'
    
    def _identify_optimal_windows(self) -> List[Dict[str, Any]]:
        """Identify optimal trading windows"""
        return [
            {'start_hour': 8, 'end_hour': 10, 'session': 'European_Main'},
            {'start_hour': 14, 'end_hour': 16, 'session': 'Overlap_Europe_US'},
            {'start_hour': 20, 'end_hour': 22, 'session': 'US_Main'}
        ]
    
    def _identify_high_volatility_periods(self) -> List[Dict[str, Any]]:
        """Identify high volatility periods"""
        return [
            {'start_hour': 13, 'end_hour': 16, 'reason': 'Europe-US overlap'},
            {'start_hour': 20, 'end_hour': 22, 'reason': 'US market peak'}
        ]
    
    def _identify_low_activity_periods(self) -> List[Dict[str, Any]]:
        """Identify periods to avoid trading"""
        return [
            {'start_hour': 22, 'end_hour': 2, 'reason': 'Market quiet'},
            {'start_hour': 5, 'end_hour': 7, 'reason': 'Pre-European low volume'}
        ]
    
    def _calculate_timing_score(self, dt: datetime, session: str) -> float:
        """Calculate timing score for specific time"""
        base_score = 0.5
        
        # Session bonus
        if session in ['Overlap_Europe_US', 'European_Main', 'US_Main']:
            base_score += 0.2
        elif session == 'Overlap_Asia_Europe':
            base_score += 0.1
        
        # Hour bonus
        if self._is_optimal_trading_window(dt, session):
            base_score += 0.2
        
        # Day penalty
        if dt.weekday() >= 5:
            base_score -= 0.3
        
        return max(0.0, min(1.0, base_score))
    
    def _get_timing_grade(self, score: float) -> str:
        """Convert timing score to grade"""
        if score >= 0.8:
            return 'A'
        elif score >= 0.6:
            return 'B'
        elif score >= 0.4:
            return 'C'
        elif score >= 0.2:
            return 'D'
        else:
            return 'F'
    
    def _get_timing_recommendations(self, score: float) -> List[str]:
        """Get recommendations based on timing score"""
        if score >= 0.8:
            return ["Excellent timing", "Continue with current approach"]
        elif score >= 0.6:
            return ["Good timing", "Minor optimization possible"]
        else:
            return ["Poor timing", "Consider waiting for better windows", "Review market session schedule"]
    
    def _is_historically_optimal_hour(self, hour: int, day_of_week: int) -> bool:
        """Check if hour is historically optimal"""
        optimal_hours = self._get_optimal_hours()
        return hour in optimal_hours and day_of_week < 5
    
    def _calculate_window_confidence(self, dt: datetime) -> float:
        """Calculate confidence score for trading window"""
        session = self._get_current_market_session(dt)
        base_score = 0.5
        
        if self._is_optimal_trading_window(dt, session):
            base_score += 0.3
        
        if dt.weekday() < 5:  # Weekday
            base_score += 0.1
        
        volatility = self._get_expected_volatility(dt, session)
        base_score += volatility * 0.1
        
        return min(1.0, base_score)
    
    def _get_recommended_strategies_for_time(self, dt: datetime) -> List[str]:
        """Get recommended strategies for specific time"""
        session = self._get_current_market_session(dt)
        volatility = self._get_expected_volatility(dt, session)
        
        if volatility > 0.8:
            return ['momentum', 'trend_following']
        elif volatility > 0.5:
            return ['momentum', 'mean_reversion']
        else:
            return ['mean_reversion', 'range_trading']
    
    def _is_near_major_news_time(self, dt: datetime) -> bool:
        """Check if near major news release times"""
        # Common news release times (UTC)
        news_hours = [8, 12, 13, 14, 15]  # ECB, Fed, major economic data
        return dt.hour in news_hours
