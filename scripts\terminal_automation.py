"""
Terminal Automation Helper for Enhanced Command Execution
Provides autonomous terminal operations with error recovery
"""

import os
import sys
import subprocess
import asyncio
import json
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import logging
from datetime import datetime
import shlex
import platform

class TerminalAutomation:
    """Enhanced terminal automation with AI-like capabilities"""
    
    def __init__(self):
        self.logger = logging.getLogger("TerminalAutomation")
        self.conda_env = "autogpt-trader"
        self.project_root = Path("E:/The_real_deal_copy/Bybit_Bot/BOT")
        self.command_history = []
        self.error_patterns = {
            "ModuleNotFoundError": self.fix_module_not_found,
            "ImportError": self.fix_import_error,
            "SyntaxError": self.fix_syntax_error,
            "FileNotFoundError": self.fix_file_not_found,
            "PermissionError": self.fix_permission_error,
            "ConnectionError": self.fix_connection_error,
            "conda activate": self.fix_conda_activation,
            "pip install": self.fix_pip_install
        }
        
    async def execute_command(self, command: str, retry_count: int = 3) -> Tuple[bool, str, str]:
        """Execute command with automatic error recovery"""
        for attempt in range(retry_count):
            try:
                # Prepare command for execution
                if platform.system() == "Windows":
                    full_command = f"cmd /c conda activate {self.conda_env} && {command}"
                else:
                    full_command = f"source activate {self.conda_env} && {command}"
                
                # Execute command
                process = await asyncio.create_subprocess_shell(
                    full_command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=str(self.project_root)
                )
                
                stdout, stderr = await process.communicate()
                stdout_str = stdout.decode('utf-8', errors='ignore')
                stderr_str = stderr.decode('utf-8', errors='ignore')
                
                # Log command execution
                self.command_history.append({
                    "command": command,
                    "timestamp": datetime.now().isoformat(),
                    "success": process.returncode == 0,
                    "stdout": stdout_str,
                    "stderr": stderr_str
                })
                
                if process.returncode == 0:
                    return True, stdout_str, stderr_str
                else:
                    # Try to fix the error
                    fixed_command = await self.auto_fix_error(command, stderr_str)
                    if fixed_command and fixed_command != command:
                        command = fixed_command
                        continue
                    
            except Exception as e:
                self.logger.error(f"Command execution failed: {e}")
                
        return False, "", f"Failed after {retry_count} attempts"
    
    async def auto_fix_error(self, command: str, error: str) -> Optional[str]:
        """Automatically fix common errors"""
        for pattern, fix_func in self.error_patterns.items():
            if pattern in error:
                return await fix_func(command, error)
        return None
    
    async def fix_module_not_found(self, command: str, error: str) -> Optional[str]:
        """Fix missing module errors"""
        # Extract module name from error
        import re
        match = re.search(r"No module named '(\w+)'", error)
        if match:
            module = match.group(1)
            # Try to install the module
            install_cmd = f"pip install {module}"
            success, _, _ = await self.execute_command(install_cmd, retry_count=1)
            if success:
                return command  # Retry original command
        return None
    
    async def fix_import_error(self, command: str, error: str) -> Optional[str]:
        """Fix import errors"""
        # Similar to module not found
        return await self.fix_module_not_found(command, error)
    
    async def fix_syntax_error(self, command: str, error: str) -> Optional[str]:
        """Fix syntax errors in Python commands"""
        if "python" in command:
            # Try to fix common syntax issues
            fixed = command.replace("python", "python -u")  # Unbuffered output
            return fixed
        return None
    
    async def fix_file_not_found(self, command: str, error: str) -> Optional[str]:
        """Fix file not found errors"""
        # Try to find the file in project
        import re
        match = re.search(r"'([^']+)': No such file", error)
        if match:
            filename = match.group(1)
            # Search for file
            for path in self.project_root.rglob(filename):
                # Update command with correct path
                return command.replace(filename, str(path))
        return None
    
    async def fix_permission_error(self, command: str, error: str) -> Optional[str]:
        """Fix permission errors"""
        if platform.system() == "Windows":
            # No sudo on Windows, try running as is
            return command
        else:
            # Add sudo if not present
            if not command.startswith("sudo"):
                return f"sudo {command}"
        return None
    
    async def fix_connection_error(self, command: str, error: str) -> Optional[str]:
        """Fix connection errors"""
        # Add retry logic or proxy settings
        if "pip install" in command and "--proxy" not in command:
            # Try with different index
            return command.replace("pip install", "pip install --index-url https://pypi.org/simple")
        return None
    
    async def fix_conda_activation(self, command: str, error: str) -> Optional[str]:
        """Fix conda activation issues"""
        if platform.system() == "Windows":
            # Try alternative activation method
            return command.replace("conda activate", "activate")
        return None
    
    async def fix_pip_install(self, command: str, error: str) -> Optional[str]:
        """Fix pip installation issues"""
        # Try upgrading pip first
        await self.execute_command("python -m pip install --upgrade pip", retry_count=1)
        # Try with --user flag
        if "--user" not in command:
            return command.replace("pip install", "pip install --user")
        return None
    
    async def execute_workflow(self, workflow: List[str]) -> Dict[str, Any]:
        """Execute a series of commands as a workflow"""
        results = {
            "success": True,
            "commands": [],
            "total_time": 0
        }
        
        start_time = datetime.now()
        
        for cmd in workflow:
            cmd_start = datetime.now()
            success, stdout, stderr = await self.execute_command(cmd)
            cmd_time = (datetime.now() - cmd_start).total_seconds()
            
            results["commands"].append({
                "command": cmd,
                "success": success,
                "output": stdout,
                "error": stderr,
                "execution_time": cmd_time
            })
            
            if not success:
                results["success"] = False
                self.logger.error(f"Workflow failed at command: {cmd}")
                break
        
        results["total_time"] = (datetime.now() - start_time).total_seconds()
        return results
    
    async def smart_command_suggestion(self, context: str) -> List[str]:
        """Suggest commands based on context"""
        suggestions = []
        
        context_lower = context.lower()
        
        if "test" in context_lower:
            suggestions.extend([
                "pytest -v",
                "python -m pytest tests/",
                "python -m unittest discover"
            ])
        
        if "install" in context_lower:
            suggestions.extend([
                "pip install -r requirements.txt",
                "conda install --file requirements.txt",
                "pip install --upgrade pip"
            ])
        
        if "run" in context_lower or "start" in context_lower:
            suggestions.extend([
                "python main.py",
                "python -m bybit_bot.main",
                "python run.py"
            ])
        
        if "fix" in context_lower or "format" in context_lower:
            suggestions.extend([
                "black .",
                "isort .",
                "autopep8 --in-place --recursive .",
                "flake8 ."
            ])
        
        if "git" in context_lower:
            suggestions.extend([
                "git status",
                "git add .",
                "git commit -m 'Auto commit'",
                "git push"
            ])
        
        return suggestions
    
    def save_command_history(self, filepath: str = "terminal_history.json"):
        """Save command history to file"""
        history_file = self.project_root / filepath
        with open(history_file, 'w') as f:
            json.dump(self.command_history, f, indent=2)
    
    def load_command_history(self, filepath: str = "terminal_history.json"):
        """Load command history from file"""
        history_file = self.project_root / filepath
        if history_file.exists():
            with open(history_file, 'r') as f:
                self.command_history = json.load(f)


# Command-line interface
async def main():
    """CLI for terminal automation"""
    automation = TerminalAutomation()
    
    if len(sys.argv) > 1:
        command = " ".join(sys.argv[1:])
        success, stdout, stderr = await automation.execute_command(command)
        print(stdout)
        if stderr:
            print(f"Errors: {stderr}", file=sys.stderr)
        sys.exit(0 if success else 1)
    else:
        # Interactive mode
        print("Terminal Automation Helper - Interactive Mode")
        print("Type 'exit' to quit, 'suggest <context>' for suggestions")
        
        while True:
            try:
                user_input = input("\n> ")
                
                if user_input.lower() == "exit":
                    break
                
                if user_input.startswith("suggest "):
                    context = user_input[8:]
                    suggestions = await automation.smart_command_suggestion(context)
                    print("Suggestions:")
                    for i, cmd in enumerate(suggestions, 1):
                        print(f"  {i}. {cmd}")
                    continue
                
                if user_input.startswith("workflow "):
                    # Execute multiple commands
                    commands = user_input[9:].split(";")
                    results = await automation.execute_workflow(commands)
                    print(f"Workflow {'succeeded' if results['success'] else 'failed'}")
                    print(f"Total time: {results['total_time']:.2f} seconds")
                    continue
                
                # Execute single command
                success, stdout, stderr = await automation.execute_command(user_input)
                if stdout:
                    print(stdout)
                if stderr:
                    print(f"Errors: {stderr}", file=sys.stderr)
                    
            except KeyboardInterrupt:
                print("\nInterrupted")
                break
            except Exception as e:
                print(f"Error: {e}", file=sys.stderr)
        
        # Save history before exit
        automation.save_command_history()
        print("\nCommand history saved.")


if __name__ == "__main__":
    asyncio.run(main())
