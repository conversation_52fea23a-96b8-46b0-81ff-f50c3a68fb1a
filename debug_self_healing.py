#!/usr/bin/env python3
"""
Debug script for Self-Healing System
Tests and debugs the autonomous error recovery system
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger, setup_logging
from bybit_bot.core.self_healing import SelfHealingSystem, SystemComponent, ErrorSeverity
from bybit_bot.database.connection import DatabaseManager


async def debug_self_healing():
    """Debug the self-healing system"""
    
    # Setup logging
    setup_logging(log_level="DEBUG", console_output=True)
    logger = TradingBotLogger("DebugSelfHealing")
    
    logger.info("Starting Self-Healing System Debug Session")
    
    try:
        # Initialize configuration
        config = BotConfig()
        logger.info("Configuration loaded successfully")
        
        # Initialize database
        db_manager = DatabaseManager(config)
        await db_manager.connect()
        logger.info("Database connected")
        
        # Initialize self-healing system
        healing_system = SelfHealingSystem(config, db_manager)
        await healing_system.initialize()
        logger.info("Self-Healing System initialized")
        
        # Test system health monitoring
        logger.info("Testing system health monitoring...")
        health_status = await healing_system.get_system_health()
        logger.info(f"System health status: {health_status}")
        
        # Test error reporting
        logger.info("Testing error reporting...")
        
        # Simulate various types of errors
        test_errors = [
            (SystemComponent.API_CLIENT, Exception("Connection timeout"), ErrorSeverity.MEDIUM),
            (SystemComponent.DATABASE, Exception("Query failed"), ErrorSeverity.HIGH),
            (SystemComponent.TRADING_ENGINE, Exception("Order placement failed"), ErrorSeverity.HIGH),
            (SystemComponent.ML_PREDICTOR, Exception("Model prediction error"), ErrorSeverity.LOW),
            (SystemComponent.DATA_CRAWLER, Exception("Data fetch timeout"), ErrorSeverity.MEDIUM)
        ]
        
        error_ids = []
        for component, error, severity in test_errors:
            error_id = await healing_system.report_error(component, error, severity)
            error_ids.append(error_id)
            logger.info(f"Reported error {error_id} for {component.value}")
        
        # Wait for healing system to process errors
        await asyncio.sleep(2)
        
        # Test manual healing
        logger.info("Testing manual healing...")
        healing_result = await healing_system.heal_component(SystemComponent.API_CLIENT, force=True)
        logger.info(f"Manual healing result: {healing_result}")
        
        # Test circuit breaker functionality
        logger.info("Testing circuit breaker functionality...")
        
        # Simulate multiple failures to trigger circuit breaker
        for i in range(6):  # Exceed failure threshold
            await healing_system.report_error(
                SystemComponent.API_CLIENT,
                Exception(f"Repeated failure {i+1}"),
                ErrorSeverity.MEDIUM
            )
        
        # Check circuit breaker status
        health_after_failures = await healing_system.get_system_health()
        circuit_breaker_status = health_after_failures.get('circuit_breaker_status', {})
        logger.info(f"Circuit breaker status: {circuit_breaker_status}")
        
        # Test emergency mode
        logger.info("Testing emergency mode...")
        await healing_system.enable_emergency_mode("Debug test emergency mode")
        
        # Check system status in emergency mode
        emergency_health = await healing_system.get_system_health()
        logger.info(f"Emergency mode status: {emergency_health.get('emergency_mode', False)}")
        
        # Wait a moment
        await asyncio.sleep(1)
        
        # Disable emergency mode
        await healing_system.disable_emergency_mode()
        logger.info("Emergency mode disabled")
        
        # Test predictive healing
        logger.info("Testing predictive healing capabilities...")
        
        # Simulate system stress to trigger predictive analysis
        for i in range(3):
            await healing_system.report_error(
                SystemComponent.MEMORY_MANAGER,
                Exception(f"Memory usage warning {i+1}"),
                ErrorSeverity.LOW
            )
        
        # Wait for predictive analysis
        await asyncio.sleep(3)
        
        # Test recovery strategy optimization
        logger.info("Testing recovery strategy optimization...")
        
        # Get current healing metrics
        final_health = await healing_system.get_system_health()
        healing_metrics = final_health.get('healing_metrics', {})
        logger.info(f"Final healing metrics: {healing_metrics}")
        
        # Test component health monitoring
        logger.info("Testing individual component health...")
        for component in SystemComponent:
            try:
                health = await getattr(healing_system, '_check_component_health')(component)
                logger.info(f"{component.value} health: {health:.2f}")
            except Exception as e:
                logger.warning(f"Could not check {component.value} health: {e}")

        # Test system metrics collection
        logger.info("Testing system metrics collection...")
        metrics = await getattr(healing_system, '_collect_health_metrics')()
        logger.info(f"System metrics: CPU={metrics.cpu_usage:.1f}%, Memory={metrics.memory_usage:.1f}%, Disk={metrics.disk_usage:.1f}%")
        
        # Test error pattern analysis
        logger.info("Testing error pattern analysis...")
        await getattr(healing_system, '_analyze_error_patterns')()

        # Test failure prediction
        logger.info("Testing failure prediction...")
        predictions = await getattr(healing_system, '_predict_failures')()
        logger.info(f"Failure predictions: {len(predictions)} potential issues identified")
        
        logger.info("Self-Healing System debug session completed successfully")
        
    except Exception as e:
        logger.error(f"Debug session failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        if 'healing_system' in locals():
            await healing_system.shutdown()
            logger.info("Self-healing system shutdown")
        
        if 'db_manager' in locals():
            await db_manager.close()
            logger.info("Database connection closed")


if __name__ == "__main__":
    print("Self-Healing System Debug Session")
    print("=" * 50)
    
    # Run debug session
    asyncio.run(debug_self_healing())
    
    print("=" * 50)
    print("Debug session completed")
