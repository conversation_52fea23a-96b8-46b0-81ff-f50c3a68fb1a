import { TrendingDown, TrendingUp } from '@mui/icons-material'
import {
    Box,
    Card,
    CardContent,
    Chip,
    LinearProgress,
    List,
    ListItem,
    ListItemText,
    Typography,
} from '@mui/material'
import { motion } from 'framer-motion'

const StrategyPerformance = ({ performanceMetrics = null }) => {
    // Use real-time performance metrics or fallback to mock data
    const mockStrategies = [
        {
            name: 'Arbitrage Scanner',
            performance: 23.4,
            trades: 156,
            winRate: 78.2,
            status: 'active',
            profit: 12450.32,
        },
        {
            name: 'Grid Trading',
            performance: 18.7,
            trades: 89,
            winRate: 71.9,
            status: 'active',
            profit: 8923.45,
        },
        {
            name: 'Momentum Scalping',
            performance: 31.2,
            trades: 234,
            winRate: 68.4,
            status: 'active',
            profit: 15673.21,
        },
        {
            name: 'Mean Reversion',
            performance: 15.9,
            trades: 67,
            winRate: 75.3,
            status: 'paused',
            profit: 6432.18,
        },
        {
            name: 'News Trading',
            performance: 42.1,
            trades: 23,
            winRate: 82.6,
            status: 'active',
            profit: 9876.54,
        },
    ]

    // Use real-time strategy metrics if available, otherwise use mock data
    const strategies = performanceMetrics?.strategies || mockStrategies

    const getStatusColor = (status) => {
        switch (status) {
            case 'active':
                return '#00ff88'
            case 'paused':
                return '#ffa726'
            case 'stopped':
                return '#ff4757'
            default:
                return '#666'
        }
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
        >
            <Card
                sx={{
                    background: 'rgba(255, 255, 255, 0.03)',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: 3,
                    height: '400px',
                }}
            >
                <CardContent sx={{ height: '100%', p: 3 }}>
                    <Typography
                        variant="h6"
                        sx={{
                            fontWeight: 600,
                            color: '#ffffff',
                            mb: 3,
                        }}
                    >
                        Strategy Performance
                    </Typography>

                    <List sx={{ py: 0, overflow: 'auto', maxHeight: 'calc(100% - 60px)' }}>
                        {strategies.map((strategy, index) => (
                            <motion.div
                                key={strategy.name}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.4, delay: index * 0.1 }}
                            >
                                <ListItem
                                    sx={{
                                        px: 0,
                                        py: 1.5,
                                        borderBottom: index < strategies.length - 1 ? '1px solid rgba(255, 255, 255, 0.05)' : 'none',
                                    }}
                                >
                                    <ListItemText
                                        primary={
                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                                <Typography variant="body2" sx={{ color: '#ffffff', fontWeight: 600 }}>
                                                    {strategy.name}
                                                </Typography>
                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    {strategy.performance > 0 ? (
                                                        <TrendingUp sx={{ color: '#00ff88', fontSize: 16 }} />
                                                    ) : (
                                                        <TrendingDown sx={{ color: '#ff4757', fontSize: 16 }} />
                                                    )}
                                                    <Typography
                                                        variant="caption"
                                                        sx={{
                                                            color: strategy.performance > 0 ? '#00ff88' : '#ff4757',
                                                            fontWeight: 600,
                                                        }}
                                                    >
                                                        {strategy.performance > 0 ? '+' : ''}{strategy.performance}%
                                                    </Typography>
                                                </Box>
                                            </Box>
                                        }
                                        secondary={
                                            <Box>
                                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                                                    <Typography variant="caption" sx={{ color: '#b3b3b3' }}>
                                                        {strategy.trades} trades • {strategy.winRate}% win rate
                                                    </Typography>
                                                    <Chip
                                                        label={strategy.status}
                                                        size="small"
                                                        sx={{
                                                            backgroundColor: `${getStatusColor(strategy.status)}20`,
                                                            color: getStatusColor(strategy.status),
                                                            fontSize: '0.7rem',
                                                            height: 20,
                                                        }}
                                                    />
                                                </Box>
                                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                                                    <Typography variant="caption" sx={{ color: '#00ff88' }}>
                                                        ${strategy.profit.toLocaleString()}
                                                    </Typography>
                                                </Box>
                                                <LinearProgress
                                                    variant="determinate"
                                                    value={strategy.winRate}
                                                    sx={{
                                                        height: 3,
                                                        borderRadius: 2,
                                                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                                                        '& .MuiLinearProgress-bar': {
                                                            backgroundColor: strategy.winRate > 75 ? '#00ff88' : strategy.winRate > 60 ? '#ffa726' : '#ff4757',
                                                            borderRadius: 2,
                                                        },
                                                    }}
                                                />
                                            </Box>
                                        }
                                    />
                                </ListItem>
                            </motion.div>
                        ))}
                    </List>
                </CardContent>
            </Card>
        </motion.div>
    )
}

export default StrategyPerformance
