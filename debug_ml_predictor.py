#!/usr/bin/env python3
"""
Debug script for ML Market Predictor
Tests and debugs the machine learning prediction system
"""

import asyncio
import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger, setup_logging
from bybit_bot.ml.market_predictor import MLMarketPredictor
from bybit_bot.database.connection import DatabaseManager


async def debug_ml_predictor():
    """Debug the ML market predictor"""
    
    # Setup logging
    setup_logging(log_level="DEBUG", console_output=True)
    logger = TradingBotLogger("DebugMLPredictor")
    
    logger.info("Starting ML Market Predictor Debug Session")
    
    try:
        # Initialize configuration
        config = BotConfig()
        logger.info("Configuration loaded successfully")
        
        # Initialize database
        db_manager = DatabaseManager(config)
        await db_manager.connect()
        logger.info("Database connected")
        
        # Initialize ML predictor
        predictor = MLMarketPredictor(config, db_manager)
        await predictor.initialize()
        logger.info("ML Market Predictor initialized")
        
        # Test data preparation
        logger.info("Testing data preparation...")
        
        # Create sample market data
        sample_data = pd.DataFrame({
            'timestamp': pd.date_range('2024-01-01', periods=1000, freq='1min'),
            'open_price': np.random.randn(1000).cumsum() + 50000,
            'high_price': np.random.randn(1000).cumsum() + 50100,
            'low_price': np.random.randn(1000).cumsum() + 49900,
            'close_price': np.random.randn(1000).cumsum() + 50000,
            'volume': np.random.exponential(1000, 1000)
        })
        
        logger.info(f"Sample data created: {len(sample_data)} rows")
        
        # Test feature engineering
        logger.info("Testing feature engineering...")
        features = await predictor.prepare_features(sample_data, 'BTCUSDT')
        logger.info(f"Features prepared: {features.shape if features is not None else 'None'}")
        
        if features is not None and len(features) > 0:
            logger.info(f"Feature columns: {list(features.columns)}")
            
            # Test model training
            logger.info("Testing model training...")
            training_result = await predictor.train_models(features, 'BTCUSDT')
            logger.info(f"Training result: {training_result}")
            
            # Test predictions
            logger.info("Testing predictions...")
            recent_data = features.tail(100)  # Last 100 rows
            predictions = await predictor.predict_price_movement(recent_data, 'BTCUSDT')
            logger.info(f"Predictions: {predictions}")
            
            # Test model evaluation
            logger.info("Testing model evaluation...")
            evaluation = await predictor.evaluate_models('BTCUSDT')
            logger.info(f"Model evaluation: {evaluation}")
            
            # Test feature importance
            logger.info("Testing feature importance...")
            importance = await predictor.get_feature_importance('BTCUSDT')
            logger.info(f"Feature importance: {importance}")
        
        # Test model persistence
        logger.info("Testing model persistence...")
        save_result = await predictor.save_models('BTCUSDT')
        logger.info(f"Model save result: {save_result}")
        
        load_result = await predictor.load_models('BTCUSDT')
        logger.info(f"Model load result: {load_result}")
        
        # Test real-time prediction
        logger.info("Testing real-time prediction...")
        if len(sample_data) > 50:
            latest_data = sample_data.tail(50)
            realtime_prediction = await predictor.get_realtime_prediction(latest_data, 'BTCUSDT')
            logger.info(f"Real-time prediction: {realtime_prediction}")
        
        logger.info("ML Market Predictor debug session completed successfully")
        
    except Exception as e:
        logger.error(f"Debug session failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        if 'db_manager' in locals():
            await db_manager.close()
            logger.info("Database connection closed")


if __name__ == "__main__":
    print("ML Market Predictor Debug Session")
    print("=" * 50)
    
    # Run debug session
    asyncio.run(debug_ml_predictor())
    
    print("=" * 50)
    print("Debug session completed")
