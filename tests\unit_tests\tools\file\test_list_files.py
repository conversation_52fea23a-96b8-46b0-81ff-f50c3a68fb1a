#!/usr/bin/env python3
"""
Real File System Test - NO MOCK DATA
Tests actual file system operations with real paths
"""

import pytest
import os
from pathlib import Path

def test_list_real_project_files():
    """Test listing real project files - NO MOCK DATA"""
    
    # Test with actual project directory
    project_root = Path(__file__).parent.parent.parent.parent
    bybit_bot_dir = project_root / "bybit_bot"
    
    if bybit_bot_dir.exists():
        files = list(bybit_bot_dir.iterdir())
        assert len(files) > 0
        
        # Check for real system directories
        dir_names = [f.name for f in files if f.is_dir()]
        expected_dirs = ['core', 'exchange', 'agents', 'ai', 'database']
        found_dirs = [d for d in expected_dirs if d in dir_names]
        assert len(found_dirs) > 0, f"Expected dirs {expected_dirs}, found {dir_names}"

def test_list_real_config_files():
    """Test listing real configuration files"""
    project_root = Path(__file__).parent.parent.parent.parent
    
    # Check for real config files
    config_files = ['config_template.yaml', 'requirements.txt', 'main.py']
    existing_files = [f for f in config_files if (project_root / f).exists()]
    
    assert len(existing_files) > 0, f"No real config files found in {project_root}"

def test_real_directory_structure():
    """Test real directory structure exists"""
    project_root = Path(__file__).parent.parent.parent.parent
    
    # Verify real system structure
    required_paths = [
        project_root / "bybit_bot",
        project_root / "logs",
        project_root / "data"
    ]
    
    existing_paths = [p for p in required_paths if p.exists()]
    assert len(existing_paths) > 0, "No real system directories found"