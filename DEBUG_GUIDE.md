# Bybit Trading Bot - Complete Debug Guide

This guide provides comprehensive debugging capabilities for the entire Bybit Trading Bot workspace.

## 🚀 Quick Start

### Prerequisites
- VS Code with Python extension
- Conda environment `bybit-trader` activated
- All dependencies installed

### Launch Debug Session
1. Open VS Code in the workspace root
2. Press `F5` or go to Run and Debug panel
3. Select your desired debug configuration
4. Set breakpoints as needed
5. Start debugging!

## 🔧 Debug Configurations

### Main Trading Bot Configurations

#### 🚀 Launch Autonomous Trading Bot
- **Purpose**: Debug the main trading bot entry point
- **File**: `main.py`
- **Use Case**: Full system debugging, end-to-end testing

#### 🔴 Debug with Breakpoints
- **Purpose**: Step-through debugging with automatic breakpoint stop
- **File**: `main.py`
- **Use Case**: Detailed code inspection, variable examination

#### 🎯 Debug Trading Bot with Config
- **Purpose**: Debug with verbose logging and configuration options
- **File**: `main.py`
- **Use Case**: Configuration testing, environment debugging

### Component-Specific Debugging

#### 🔍 Debug Enhanced Bybit Client
- **Purpose**: Test API connectivity and enhanced client features
- **File**: `debug_enhanced_client.py`
- **Features**:
  - API connection testing
  - Account information retrieval
  - Market data fetching
  - Rate limiting verification
  - Error handling validation

#### 🤖 Debug ML Market Predictor
- **Purpose**: Test machine learning prediction system
- **File**: `debug_ml_predictor.py`
- **Features**:
  - Feature engineering testing
  - Model training validation
  - Prediction accuracy testing
  - Model persistence verification

#### 📡 Debug Data Crawler
- **Purpose**: Test data collection and processing
- **File**: `debug_data_crawler.py`
- **Features**:
  - Market data collection
  - News sentiment analysis
  - Social media monitoring
  - Data storage validation

#### ⚠️ Debug Risk Manager
- **Purpose**: Test risk management and position sizing
- **File**: `debug_risk_manager.py`
- **Features**:
  - Portfolio risk assessment
  - Position sizing calculations
  - Trade validation
  - Drawdown monitoring

#### 🛠️ Debug Self-Healing System
- **Purpose**: Test autonomous error recovery
- **File**: `debug_self_healing.py`
- **Features**:
  - Error reporting and handling
  - Circuit breaker functionality
  - System health monitoring
  - Recovery strategy testing

#### 🗄️ Debug Database Connection
- **Purpose**: Test database connectivity and operations
- **File**: `debug_database.py`
- **Features**:
  - Connection testing
  - CRUD operations
  - Transaction handling
  - Performance monitoring

#### ⏰ Debug Time Manager
- **Purpose**: Test market timing and scheduling
- **File**: `debug_time_manager.py`
- **Features**:
  - Market session detection
  - Optimal trading hours
  - Holiday calendar integration
  - Time-based analytics

### Utility Configurations

#### 📄 Debug Current File
- **Purpose**: Debug any currently open Python file
- **Use Case**: Quick testing of individual modules

#### 🧪 Debug Tests
- **Purpose**: Run and debug pytest test suite
- **Use Case**: Test validation and debugging

## 🎯 Compound Configurations

### 🚀 Debug Full Trading System
- **Components**: Main Bot + Self-Healing System
- **Purpose**: Complete system debugging with monitoring

### 🔬 Debug Core Components
- **Components**: Enhanced Client + ML Predictor + Data Crawler
- **Purpose**: Core functionality testing

### 🛡️ Debug Safety Systems
- **Components**: Risk Manager + Self-Healing + Time Manager
- **Purpose**: Safety and monitoring system validation

## 📋 Debug Tasks

Access via `Ctrl+Shift+P` → "Tasks: Run Task"

### Component Testing Tasks
- 🔍 Debug Enhanced Client
- 🤖 Debug ML Predictor
- 📡 Debug Data Crawler
- ⚠️ Debug Risk Manager
- 🛠️ Debug Self-Healing
- 🗄️ Debug Database
- ⏰ Debug Time Manager

### Utility Tasks
- 🧹 Clean Debug Logs
- 📊 Check System Status
- 🔧 Install Dependencies

## 🔍 Debug Features

### Breakpoint Management
- Set breakpoints by clicking line numbers
- Conditional breakpoints: Right-click → "Add Conditional Breakpoint"
- Logpoints: Right-click → "Add Logpoint"

### Variable Inspection
- **Variables Panel**: View local and global variables
- **Watch Panel**: Monitor specific expressions
- **Call Stack**: Navigate execution hierarchy

### Debug Console
- Execute Python expressions during debugging
- Access current scope variables
- Test function calls interactively

### Step Controls
- **Continue (F5)**: Resume execution
- **Step Over (F10)**: Execute next line
- **Step Into (F11)**: Enter function calls
- **Step Out (Shift+F11)**: Exit current function

## 🛠️ Troubleshooting

### Common Issues

#### Import Errors
```bash
# Ensure Python path is set correctly
export PYTHONPATH="${PYTHONPATH}:/path/to/workspace"
```

#### Environment Issues
```bash
# Activate conda environment
conda activate bybit-trader

# Verify Python interpreter
which python
```

#### Database Connection Issues
```bash
# Check PostgreSQL service
# Verify connection string in config
# Test database connectivity
```

### Debug Log Locations
- **Application Logs**: `logs/`
- **Debug Logs**: Console output during debug sessions
- **Error Logs**: Captured in debug console

## 📈 Performance Debugging

### Memory Profiling
- Use debug console to check memory usage
- Monitor variable sizes in Variables panel
- Track object creation/destruction

### Execution Timing
- Use logpoints to measure execution time
- Monitor function call frequency
- Identify performance bottlenecks

## 🔒 Security Considerations

### API Keys
- Never commit API keys to version control
- Use environment variables or secure config files
- Rotate keys regularly

### Debug Data
- Avoid logging sensitive information
- Clean debug logs regularly
- Use secure connections for remote debugging

## 📚 Additional Resources

### VS Code Documentation
- [Python Debugging](https://code.visualstudio.com/docs/python/debugging)
- [Launch Configurations](https://code.visualstudio.com/docs/editor/debugging#_launch-configurations)

### Python Debugging
- [pdb Documentation](https://docs.python.org/3/library/pdb.html)
- [Debugging Best Practices](https://realpython.com/python-debugging-pdb/)

## 🎯 Best Practices

1. **Start Small**: Debug individual components before full system
2. **Use Breakpoints**: Strategic placement for efficient debugging
3. **Monitor Resources**: Watch memory and CPU usage during debugging
4. **Clean Logs**: Regular cleanup of debug output
5. **Test Incrementally**: Validate changes step by step
6. **Document Issues**: Keep track of bugs and solutions

## 🚀 Next Steps

1. Choose appropriate debug configuration
2. Set relevant breakpoints
3. Start debug session
4. Inspect variables and execution flow
5. Fix issues and re-test
6. Validate with full system test

Happy debugging! 🐛🔍
