import {
    Box,
    Card,
    CardContent,
    ToggleButton,
    ToggleButtonGroup,
    Typography,
} from '@mui/material'
import { motion } from 'framer-motion'
import { useState } from 'react'
import {
    Area,
    AreaChart,
    CartesianGrid,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis
} from 'recharts'

const ProfitChart = ({ profitData = null }) => {
    const [timeframe, setTimeframe] = useState('24h')

    // Use real-time profit data or fallback to mock data
    const realTimeData = profitData || []

    // Generate chart data based on real-time data or mock data
    const generateChartData = (timeframe) => {
        if (realTimeData && realTimeData.length > 0) {
            // Use real-time data
            return realTimeData.map((item, index) => ({
                time: item.timestamp ? new Date(item.timestamp).toLocaleTimeString() : `Point ${index + 1}`,
                profit: item.value || item.profit || 0,
                volume: item.volume || Math.random() * 50000 + 10000,
            }))
        }
        
        // Fallback to mock data
        const mockData = {
            '1h': Array.from({ length: 60 }, (_, i) => ({
                time: `${String(new Date().getHours() - Math.floor((59 - i) / 60)).padStart(2, '0')}:${String(
                    new Date().getMinutes() - ((59 - i) % 60)
                ).padStart(2, '0')}`,
                profit: 125000 + Math.sin(i * 0.1) * 2000 + Math.random() * 1000,
                volume: Math.random() * 50000 + 10000,
            })),
            '24h': Array.from({ length: 24 }, (_, i) => ({
                time: `${String(i).padStart(2, '0')}:00`,
                profit: 125000 + Math.sin(i * 0.3) * 5000 + Math.random() * 2000,
                volume: Math.random() * 100000 + 20000,
            })),
            '7d': Array.from({ length: 7 }, (_, i) => ({
                time: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', {
                    weekday: 'short',
                }),
                profit: 125000 + Math.sin(i * 0.5) * 10000 + Math.random() * 5000,
                volume: Math.random() * 200000 + 50000,
            })),
        }
        return mockData[timeframe]
    }

    const currentData = generateChartData(timeframe)

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            return (
                <Box
                    sx={{
                        background: 'rgba(0, 0, 0, 0.9)',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        borderRadius: 2,
                        p: 2,
                        backdropFilter: 'blur(10px)',
                    }}
                >
                    <Typography variant="body2" sx={{ color: '#b3b3b3', mb: 1 }}>
                        {label}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#00ff88' }}>
                        Profit: ${payload[0].value.toLocaleString()}
                    </Typography>
                    {payload[1] && (
                        <Typography variant="body2" sx={{ color: '#42a5f5' }}>
                            Volume: ${payload[1].value.toLocaleString()}
                        </Typography>
                    )}
                </Box>
            )
        }
        return null
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
        >
            <Card
                sx={{
                    background: 'rgba(255, 255, 255, 0.03)',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: 3,
                    height: '400px',
                }}
            >
                <CardContent sx={{ height: '100%', p: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                        <Typography
                            variant="h6"
                            sx={{
                                fontWeight: 600,
                                color: '#ffffff',
                            }}
                        >
                            Portfolio Performance
                        </Typography>

                        <ToggleButtonGroup
                            value={timeframe}
                            exclusive
                            onChange={(_, newTimeframe) => newTimeframe && setTimeframe(newTimeframe)}
                            sx={{
                                '& .MuiToggleButton-root': {
                                    color: '#b3b3b3',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                    px: 2,
                                    py: 0.5,
                                    fontSize: '0.875rem',
                                    '&.Mui-selected': {
                                        backgroundColor: 'rgba(0, 255, 136, 0.1)',
                                        color: '#00ff88',
                                        border: '1px solid rgba(0, 255, 136, 0.3)',
                                    },
                                    '&:hover': {
                                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                                    },
                                },
                            }}
                        >
                            <ToggleButton value="1h">1H</ToggleButton>
                            <ToggleButton value="24h">24H</ToggleButton>
                            <ToggleButton value="7d">7D</ToggleButton>
                        </ToggleButtonGroup>
                    </Box>

                    <Box sx={{ height: 'calc(100% - 80px)' }}>
                        <ResponsiveContainer width="100%" height="100%">
                            <AreaChart data={currentData}>
                                <defs>
                                    <linearGradient id="profitGradient" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor="#00ff88" stopOpacity={0.3} />
                                        <stop offset="95%" stopColor="#00ff88" stopOpacity={0} />
                                    </linearGradient>
                                </defs>
                                <CartesianGrid
                                    strokeDasharray="3 3"
                                    stroke="rgba(255, 255, 255, 0.1)"
                                    vertical={false}
                                />
                                <XAxis
                                    dataKey="time"
                                    stroke="#b3b3b3"
                                    fontSize={12}
                                    axisLine={false}
                                    tickLine={false}
                                />
                                <YAxis
                                    stroke="#b3b3b3"
                                    fontSize={12}
                                    axisLine={false}
                                    tickLine={false}
                                    tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                                />
                                <Tooltip content={<CustomTooltip />} />
                                <Area
                                    type="monotone"
                                    dataKey="profit"
                                    stroke="#00ff88"
                                    strokeWidth={2}
                                    fill="url(#profitGradient)"
                                    dot={false}
                                />
                            </AreaChart>
                        </ResponsiveContainer>
                    </Box>
                </CardContent>
            </Card>
        </motion.div>
    )
}

export default ProfitChart
