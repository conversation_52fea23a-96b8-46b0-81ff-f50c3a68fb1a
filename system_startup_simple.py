#!/usr/bin/env python3
"""
SIMPLIFIED SYSTEM STARTUP - BYBIT TRADING BOT
Clean startup script without Unicode characters for Windows compatibility
"""

import sys
import os
import asyncio
import logging
import time
from pathlib import Path
from datetime import datetime

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

class SimpleSystemInitializer:
    """Simplified system initializer without Unicode characters"""
    
    def __init__(self):
        self.setup_basic_logging()
        self.components = {}
        self.errors = []
        self.warnings = []
        self.start_time = time.time()
        
    def setup_basic_logging(self):
        """Setup basic logging without Unicode"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('system_startup.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger("SystemStartup")
    
    def initialize_config(self):
        """Initialize configuration"""
        try:
            self.logger.info("Initializing configuration...")
            from bybit_bot.core.config import get_config
            
            config = get_config()
            self.components['config'] = config
            self.logger.info("Configuration loaded successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize config: {e}")
            self.errors.append(f"Config initialization: {e}")
            return False
    
    def initialize_database(self):
        """Initialize database with fallback to SQLite"""
        try:
            self.logger.info("Initializing database...")
            from bybit_bot.database.connection import DatabaseManager
            
            config = self.components.get('config')
            if not config:
                self.logger.error("Config not available for database initialization")
                return False
            
            db_manager = DatabaseManager(config)
            
            # Try to initialize
            try:
                asyncio.run(db_manager.initialize())
                self.components['database'] = db_manager
                self.logger.info("Database initialized successfully")
                return True
            except Exception as db_error:
                self.logger.warning(f"Database initialization failed: {db_error}")
                self.logger.info("Database will use SQLite fallback")
                # Still consider it successful with fallback
                self.components['database'] = db_manager
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
            self.errors.append(f"Database initialization: {e}")
            return False
    
    def initialize_ai_systems(self):
        """Initialize AI systems"""
        try:
            self.logger.info("Initializing AI systems...")
            
            # Import AI components
            from bybit_bot.ai.memory_manager import MemoryManager
            from bybit_bot.ai.meta_cognition_engine import MetaCognitionEngine
            
            config = self.components.get('config')
            db_manager = self.components.get('database')
            
            if not config or not db_manager:
                self.logger.warning("Missing dependencies for AI systems")
                return False
            
            # Initialize memory manager
            memory_manager = MemoryManager(config, db_manager)
            self.components['memory_manager'] = memory_manager
            
            # Initialize meta-cognition engine  
            meta_engine = MetaCognitionEngine(config)
            self.components['meta_engine'] = meta_engine
            
            self.logger.info("AI systems initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize AI systems: {e}")
            self.errors.append(f"AI systems initialization: {e}")
            return False
    
    def initialize_trading_systems(self):
        """Initialize trading systems"""
        try:
            self.logger.info("Initializing trading systems...")
            
            from bybit_bot.strategies.adaptive_strategy_engine import AdaptiveStrategyEngine
            from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
            
            config = self.components.get('config')
            db_manager = self.components.get('database')
            
            if not config:
                self.logger.warning("Missing config for trading systems")
                return False
            
            # Initialize exchange client (can be None for testing)
            exchange_client = None
            try:
                exchange_client = EnhancedBybitClient(config)
                self.components['exchange_client'] = exchange_client
            except Exception as e:
                self.logger.warning(f"Exchange client initialization failed: {e}")
            
            # Initialize strategy engine
            strategy_engine = AdaptiveStrategyEngine(exchange_client, db_manager)
            self.components['strategy_engine'] = strategy_engine
            
            self.logger.info("Trading systems initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize trading systems: {e}")
            self.errors.append(f"Trading systems initialization: {e}")
            return False
    
    def run_system_tests(self):
        """Run basic system tests"""
        try:
            self.logger.info("Running system tests...")
            
            tests_passed = 0
            total_tests = 4
            
            # Test config
            if 'config' in self.components:
                self.logger.info("PASS: Configuration test")
                tests_passed += 1
            else:
                self.logger.error("FAIL: Configuration test")
            
            # Test database
            if 'database' in self.components:
                self.logger.info("PASS: Database test")
                tests_passed += 1
            else:
                self.logger.error("FAIL: Database test")
            
            # Test strategy engine
            if 'strategy_engine' in self.components:
                self.logger.info("PASS: Strategy engine test")
                tests_passed += 1
            else:
                self.logger.error("FAIL: Strategy engine test")
            
            # Test memory manager
            if 'memory_manager' in self.components:
                self.logger.info("PASS: Memory manager test")
                tests_passed += 1
            else:
                self.logger.error("FAIL: Memory manager test")
            
            self.logger.info(f"System tests completed: {tests_passed}/{total_tests} passed")
            return tests_passed == total_tests
            
        except Exception as e:
            self.logger.error(f"System tests failed: {e}")
            return False
    
    def generate_report(self):
        """Generate system status report"""
        end_time = time.time()
        duration = end_time - self.start_time
        
        report = f"""
================================================================================
BYBIT TRADING BOT - SYSTEM INITIALIZATION REPORT
================================================================================

INITIALIZATION TIME: {duration:.2f} seconds
COMPONENTS LOADED: {len(self.components)}
ERRORS: {len(self.errors)}
WARNINGS: {len(self.warnings)}

LOADED COMPONENTS:
"""
        
        for component_name in self.components:
            report += f"  [OK] {component_name}\n"
        
        if self.errors:
            report += "\nERRORS ENCOUNTERED:\n"
            for error in self.errors:
                report += f"  [ERROR] {error}\n"
        
        if self.warnings:
            report += "\nWARNINGS:\n"
            for warning in self.warnings:
                report += f"  [WARNING] {warning}\n"
        
        status = "OPERATIONAL" if len(self.errors) == 0 else "REQUIRES ATTENTION"
        report += f"\nSYSTEM STATUS: [{status}]\n"
        
        if len(self.errors) == 0:
            report += "\nNEXT STEPS:\n"
            report += "1. Configure API keys for live trading\n"
            report += "2. Launch autonomous trading system\n"
            report += "3. Monitor system performance\n"
        else:
            report += "\nNEXT STEPS:\n"
            report += "1. Review and fix initialization errors\n"
            report += "2. Configure missing components\n"
            report += "3. Re-run system initialization\n"
        
        report += "\n================================================================================\n"
        
        return report
    
    def initialize_complete_system(self):
        """Initialize the complete system"""
        self.logger.info("STARTING BYBIT TRADING BOT INITIALIZATION")
        self.logger.info("="*80)
        
        # Initialization steps
        steps = [
            ("Configuration", self.initialize_config),
            ("Database", self.initialize_database),
            ("AI Systems", self.initialize_ai_systems),
            ("Trading Systems", self.initialize_trading_systems),
            ("System Tests", self.run_system_tests)
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            self.logger.info(f"Starting {step_name}...")
            try:
                if step_func():
                    self.logger.info(f"Completed {step_name}")
                    success_count += 1
                else:
                    self.logger.error(f"Failed {step_name}")
            except Exception as e:
                self.logger.error(f"Error in {step_name}: {e}")
                self.errors.append(f"{step_name}: {e}")
        
        # Generate and display report
        report = self.generate_report()
        print(report)
        
        # Save report to file
        try:
            with open('system_initialization_report.txt', 'w', encoding='utf-8') as f:
                f.write(report)
        except Exception as e:
            self.logger.error(f"Failed to save report: {e}")
        
        # Return success status
        return success_count == len(steps)

def main():
    """Main entry point"""
    try:
        print("INITIALIZING AUTONOMOUS BYBIT TRADING BOT")
        print("="*80)
        
        initializer = SimpleSystemInitializer()
        success = initializer.initialize_complete_system()
        
        if success:
            print("\n[SUCCESS] SYSTEM INITIALIZATION COMPLETE")
            return 0
        else:
            print("\n[ERROR] SYSTEM INITIALIZATION INCOMPLETE - PLEASE REVIEW ERRORS")
            return 1
            
    except Exception as e:
        print(f"\n[CRITICAL ERROR] System initialization failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
