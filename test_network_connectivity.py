#!/usr/bin/env python3
"""
Network Connectivity Test for Bybit Trading Bot
Tests connection to your trading system at *************
"""

import requests
import socket
import time
import sys
from urllib.parse import urljoin

# Your trading system configuration
TRADING_SYSTEM_IP = "*************"
API_PORT = 8000
WEB_PORT = 3000

BASE_API_URL = f"http://{TRADING_SYSTEM_IP}:{API_PORT}"
BASE_WEB_URL = f"http://{TRADING_SYSTEM_IP}:{WEB_PORT}"

def test_port_connectivity(host, port, timeout=5):
    """Test if a port is reachable"""
    try:
        socket.setdefaulttimeout(timeout)
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"❌ Port test error: {e}")
        return False

def test_api_endpoint(endpoint, timeout=10):
    """Test API endpoint connectivity"""
    try:
        url = urljoin(BASE_API_URL, endpoint)
        response = requests.get(url, timeout=timeout)
        return response.status_code, response.text[:200]
    except requests.exceptions.ConnectionError:
        return None, "Connection refused - is the backend running?"
    except requests.exceptions.Timeout:
        return None, "Request timeout - server may be slow"
    except Exception as e:
        return None, f"Error: {str(e)}"

def main():
    print("🚀 Bybit Trading Bot - Network Connectivity Test")
    print(f"🎯 Testing connection to: {TRADING_SYSTEM_IP}")
    print("=" * 60)
    
    # Test basic connectivity
    print(f"\n📡 Testing port connectivity...")
    
    # Test API port
    if test_port_connectivity(TRADING_SYSTEM_IP, API_PORT):
        print(f"✅ API Port {API_PORT}: OPEN")
    else:
        print(f"❌ API Port {API_PORT}: CLOSED or UNREACHABLE")
        print(f"   Check if FastAPI is running on {TRADING_SYSTEM_IP}:{API_PORT}")
    
    # Test Web port
    if test_port_connectivity(TRADING_SYSTEM_IP, WEB_PORT):
        print(f"✅ Web Port {WEB_PORT}: OPEN")
    else:
        print(f"❌ Web Port {WEB_PORT}: CLOSED or UNREACHABLE")
        print(f"   Check if web frontend is running on {TRADING_SYSTEM_IP}:{WEB_PORT}")
    
    # Test API endpoints
    print(f"\n🔌 Testing API endpoints...")
    
    endpoints_to_test = [
        "/",
        "/system-status", 
        "/ai-status",
        "/profit-status"
    ]
    
    for endpoint in endpoints_to_test:
        status_code, response = test_api_endpoint(endpoint)
        if status_code:
            if status_code == 200:
                print(f"✅ {endpoint}: HTTP {status_code}")
            else:
                print(f"⚠️  {endpoint}: HTTP {status_code}")
        else:
            print(f"❌ {endpoint}: {response}")
    
    # Test mobile app compatibility
    print(f"\n📱 Mobile App Configuration Test...")
    
    try:
        # Test the exact endpoint mobile app will use
        mobile_api_url = f"http://{TRADING_SYSTEM_IP}:{API_PORT}/"
        response = requests.get(mobile_api_url, timeout=5)
        print(f"✅ Mobile API endpoint: {mobile_api_url}")
        print(f"   Status: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Mobile API endpoint: {mobile_api_url}")
        print(f"   Error: {str(e)}")
    
    # Network recommendations
    print(f"\n🔧 Network Configuration Status:")
    print(f"   API Backend: {BASE_API_URL}")
    print(f"   Web Frontend: {BASE_WEB_URL}")
    print(f"   Mobile App API: {BASE_API_URL}")
    
    print(f"\n📋 Next Steps:")
    print(f"   1. Ensure FastAPI backend is running: python main.py")
    print(f"   2. Start web frontend: npm run dev (in frontend folder)")
    print(f"   3. Build mobile app: npm run build:android (in mobile folder)")
    print(f"   4. Install APK on your Motorola phone")
    
    print(f"\n🔥 System Ready for:")
    print(f"   ✅ Web Access: {BASE_WEB_URL}")
    print(f"   ✅ Mobile Access: Direct API connection")
    print(f"   ✅ Real-time Trading: All endpoints configured")

if __name__ == "__main__":
    main()
